<?php
/**
 * Public Authentication Handler
 * This file handles user authentication from the public directory
 */

// Define secure access constant
define('SECURE_ACCESS', true);

session_start();
require_once '../config/db_conn.php';
require_once '../config/config.php';

// Verify that this is a POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('HTTP/1.1 405 Method Not Allowed');
    header('Location: login.php?error=invalid_request');
    exit();
}

// Validate CSRF token if it's enabled
if (isset($_POST['csrf_token'])) {
    if (!validate_csrf_token($_POST['csrf_token'])) {
        // Invalid CSRF token
        error_log("CSRF token validation failed");
        header('Location: login.php?error=invalid_token');
        exit();
    }
}

// Sanitize inputs
$username = sanitize_input($_POST['username']);
$password = $_POST['password']; // Don't sanitize password before verification

$sql = "SELECT * FROM users WHERE username=?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("s", $username);
$stmt->execute();
$result = $stmt->get_result();

// Check if user exists
if ($result->num_rows > 0) {
    $row = $result->fetch_assoc();

    // Verify password
    if (password_verify($password, $row['password'])) {
        // Set session variables
        $_SESSION['username'] = $row['username'];
        $_SESSION['role'] = $row['role'];
        $_SESSION['user_id'] = $row['id'];
        $_SESSION['last_activity'] = time();

        // Regenerate session ID to prevent session fixation
        session_regenerate_id(true);

        // Log successful login
        error_log("User {$row['username']} logged in successfully");

        // Redirect based on role
        switch ($row['role']) {
            case 'admin':
                header("Location: ../admin/admin_dashboard.php");
                break;
            case 'student':
                header("Location: student_dashboard.php");
                break;
            case 'cashier':
                header("Location: ../admin/cashier_dashboard.php");
                break;
            case 'registrar':
                header("Location: ../admin/registrar_dashboard.php");
                break;
            default:
                // Unknown role
                error_log("Unknown role: {$row['role']} for user {$row['username']}");
                header("Location: login.php?error=unknown_role");
                break;
        }
        exit();
    } else {
        // Invalid password
        error_log("Failed login attempt for user: $username - Invalid password");
        header("Location: login.php?error=invalid_credentials");
    }
} else {
    // User not found
    error_log("Failed login attempt - User not found: $username");
    header("Location: login.php?error=invalid_credentials");
}

$stmt->close();
$conn->close();
?>
