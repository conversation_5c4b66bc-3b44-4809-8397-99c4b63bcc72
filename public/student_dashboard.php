<?php
// Start output buffering
ob_start();

// Start session before including any files that might try to modify session settings
session_start();

// Define secure access constant for config.php
define('SECURE_ACCESS', true);

// Include configuration and database connection
require_once '../config/config.php';
require_once '../config/db_conn.php';

// Error reporting for development
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Session timeout check
if (session_expired()) {
    session_unset();
    session_destroy();
    header("Location: login.php");
    exit();
}

// Update last activity time
$_SESSION['last_activity'] = time();

// Redirect if user is not a student
if (!isset($_SESSION['username']) || $_SESSION['role'] !== 'student') {
    header("Location: login.php");
    exit();
}

// Fetch user information
$sql = "SELECT * FROM users WHERE username = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("s", $_SESSION['username']);
$stmt->execute();
$result = $stmt->get_result();
$user = $result->fetch_assoc();

if (!$user) {
    echo "No user found with username: " . htmlspecialchars($_SESSION['username']);
    exit;
}

// Fetch payment information
$payment_sql = "SELECT SUM(amount) as total_paid FROM payments WHERE student_id = ?";
$payment_stmt = $conn->prepare($payment_sql);
$payment_stmt->bind_param("s", $_SESSION['username']);
$payment_stmt->execute();
$payment_result = $payment_stmt->get_result();
$payment_data = $payment_result->fetch_assoc();
$total_paid = $payment_data['total_paid'] ?? 0;

// Define required fee amount for registration
$required_fee = 2440;

// Check payment status
$has_paid = ($total_paid >= $required_fee);

// Fetch student information
$student_sql = "SELECT * FROM students WHERE student_id = ?";
$student_stmt = $conn->prepare($student_sql);
$student_stmt->bind_param("s", $_SESSION['username']);
$student_stmt->execute();
$student_result = $student_stmt->get_result();
$student_data = $student_result->fetch_assoc();

// Close statements and connection
$stmt->close();
$payment_stmt->close();
$student_stmt->close();
$conn->close();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.1/css/all.min.css" />
    <link rel="stylesheet" href="assets/css/styles.css" />
    <title>Student Dashboard | DBTI Online</title>
    <link rel="icon" href="assets/img/logo.webp" type="image/png">
</head>
<body>
    <!-- Navigation Bar -->
    <nav>
        <img src="assets/img/logo.webp" alt="DBTI Logo" class="logo">
        <div class="heading">DBTI Online Registration</div>
        <div class="hamburger" onclick="toggleNavbar()">
            <div></div>
            <div></div>
            <div></div>
        </div>
        <ul id="navbar">
            <li><a href="index.php">Home</a></li>
            <li><a href="about.php">About</a></li>
            <li><a href="student_dashboard.php">Dashboard</a></li>
            <li><a href="logout.php">Logout <i class="fa-solid fa-user"></i></a></li>
        </ul>
    </nav>

    <!-- Sidebar -->
    <div class="sidebar">
        <a href="registration.php"><i class="fas fa-user-plus"></i> Register for Courses</a>
        <hr>
        <a href="#" onclick="showStudentInfo()"><i class="fas fa-user"></i> View Student Info</a>
        <hr>
        <div class="dropdown">
            <a href="#" class="dropdown-toggle">
                <span><i class="fas fa-credit-card"></i> Payment</span>
                <i class="fas fa-chevron-down"></i>
            </a>
            <div class="dropdown-menu">
                <a href="../src/Controllers/choose_payment.php"><i class="fas fa-money-bill-wave"></i> Make Payment</a>
                <a href="../src/Controllers/payment_history.php"><i class="fas fa-history"></i> Payment History</a>
            </div>
        </div>
        <hr>
        <div class="dropdown">
            <a href="#" class="dropdown-toggle">
                <span><i class="fas fa-file-alt"></i> Documents</span>
                <i class="fas fa-chevron-down"></i>
            </a>
            <div class="dropdown-menu">
                <a href="../src/Controllers/download_pdf.php"><i class="fas fa-download"></i> Download Registration Form</a>
                <a href="../src/Controllers/course_outline.php"><i class="fas fa-book"></i> Course Outline</a>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="content-box">
        <h2>Welcome, <?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?>!</h2>
        
        <div class="payment-info">
            <h3>Registration Status</h3>
            <?php if ($has_paid): ?>
                <p style="color: green;"><i class="fas fa-check-circle"></i> Payment Complete - You can now register for courses!</p>
                <p>Total Paid: K<?php echo number_format($total_paid, 2); ?></p>
            <?php else: ?>
                <p style="color: red;"><i class="fas fa-exclamation-circle"></i> Payment Required</p>
                <p>Amount Due: K<?php echo number_format($required_fee - $total_paid, 2); ?></p>
                <p>Total Required: K<?php echo number_format($required_fee, 2); ?></p>
                <?php if ($total_paid > 0): ?>
                    <p>Amount Paid: K<?php echo number_format($total_paid, 2); ?></p>
                <?php endif; ?>
            <?php endif; ?>
        </div>

        <?php if (isset($student_data) && $student_data): ?>
            <div class="payment-info">
                <h3>Current Registration</h3>
                <p><strong>Program:</strong> <?php echo htmlspecialchars($student_data['program'] ?? 'Not set'); ?></p>
                <p><strong>Technology:</strong> <?php echo htmlspecialchars($student_data['tech'] ?? 'Not set'); ?></p>
                <p><strong>Year Level:</strong> <?php echo htmlspecialchars($student_data['year_level'] ?? 'Not set'); ?></p>
                <p><strong>Status:</strong> 
                    <span style="color: <?php echo ($student_data['registration_status'] === 'registered') ? 'green' : 'orange'; ?>;">
                        <?php echo htmlspecialchars($student_data['registration_status'] ?? 'Not registered'); ?>
                    </span>
                </p>
            </div>
        <?php endif; ?>
    </div>

    <!-- Footer -->
    <footer>
        <div class="footer">
            <span>
                Copyright © 2024 Don Bosco Technological Institute. All Rights Reserved.
                <a href="https://www.dbti.ac.pg/" target="_blank">DBTI Website</a>
            </span>
        </div>
    </footer>

    <script>
        function toggleNavbar() {
            const navbar = document.getElementById('navbar');
            navbar.classList.toggle('active');
        }

        function showStudentInfo() {
            // This would show student information modal
            alert('Student information feature coming soon!');
        }
    </script>

    <style>
        /* Add basic styling for the student dashboard */
        body {
            font-family: 'Inter', sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }

        nav {
            background: linear-gradient(135deg, #ff6b00, #ff9500);
            padding: 15px 30px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        }

        .logo {
            height: 50px;
            width: auto;
            border-radius: 50%;
        }

        .heading {
            color: white;
            font-size: 1.8rem;
            font-weight: bold;
        }

        nav ul {
            list-style: none;
            display: flex;
            gap: 30px;
        }

        nav ul li a {
            color: white;
            text-decoration: none;
            font-weight: 600;
            padding: 10px 15px;
            border-radius: 25px;
            transition: all 0.3s ease;
        }

        nav ul li a:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .sidebar {
            width: 250px;
            background: white;
            padding: 20px;
            position: fixed;
            top: 80px;
            left: 0;
            height: calc(100vh - 140px);
            box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
            overflow-y: auto;
        }

        .sidebar a {
            display: block;
            padding: 15px;
            color: #333;
            text-decoration: none;
            border-radius: 8px;
            margin: 5px 0;
            transition: all 0.3s ease;
        }

        .sidebar a:hover {
            background: #f8f9fa;
            transform: translateX(5px);
        }

        .content-box {
            margin-left: 270px;
            padding: 30px;
            min-height: calc(100vh - 140px);
        }

        .payment-info {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
            margin: 20px 0;
        }

        .payment-info h3 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.5rem;
        }

        footer {
            background: #333;
            color: white;
            text-align: center;
            padding: 15px;
            position: fixed;
            bottom: 0;
            width: 100%;
        }

        footer a {
            color: #ff6b00;
            text-decoration: none;
        }

        .hamburger {
            display: none;
            flex-direction: column;
            cursor: pointer;
        }

        .hamburger div {
            width: 25px;
            height: 3px;
            background: white;
            margin: 3px 0;
        }

        @media (max-width: 768px) {
            .hamburger {
                display: flex;
            }

            nav ul {
                display: none;
                position: absolute;
                top: 100%;
                left: 0;
                width: 100%;
                background: linear-gradient(135deg, #ff6b00, #ff9500);
                flex-direction: column;
            }

            nav ul.active {
                display: flex;
            }

            .sidebar {
                display: none;
            }

            .content-box {
                margin-left: 0;
                padding: 20px;
            }
        }
    </style>
</body>
</html>
