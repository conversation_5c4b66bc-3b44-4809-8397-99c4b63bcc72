<!DOCTYPE html>
<html lang="en">
<head>

    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

<meta name="google-site-verification" content="AzDmqfLcdCjucrTPvoGzDe7cBsCR259XA3jwz5NmN_Q" />

    <meta name="description" content="DBTI Online Registration">
    <meta property="og:title" content="DBTI Online Registration and Payment System">
<meta property="og:site_name" content="DBTI Online Registration and Payment System">
<meta property="og:url" content="https://dbtionline.waghitech.com">
<meta property="og:description" content="The DBTI Online Registration and Payment System is a comprehensive digital platform designed to streamline the student registration process at Don Bosco Technological Institute.">
<meta property="og:type" content="website">
<meta property="og:image" content="5">

    <link rel="stylesheet" href="assets/css/main.css">
    <link rel="icon" href="assets/img/logo.webp" type="image/png">
    <title>DBTI Online Registration</title>

    <style>
        * {
            padding: 0;
            margin: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            color: #333;
            background-color: #f5f5f5;
            scroll-behavior: smooth;
        }
        /* Navbar */
        nav {
            background-color: #ffbf00;
            padding: 20px 5%;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
            width: 100%; /* Ensure full width */
        }

        nav .heading {
            font-size: 1.75rem;
            font-weight: bold;
            color: #fff;
        }

        nav ul {
            list-style: none;
            display: flex;
            gap: 20px;
        }

        nav ul li a {
            font-size: 1rem;
            color: #fff;
            text-transform: uppercase;
            padding: 12px 20px;
            background-color: #cc9900;
            border-radius: 6px;
            font-weight: 700;
            transition: all 0.3s ease;
            display: inline-block;
        }

        nav ul li a:hover {
            background-color: #996600;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        /* Hamburger Menu */
        .hamburger {
            display: none;
            flex-direction: column;
            cursor: pointer;
        }

        .hamburger div {
            width: 25px;
            height: 3px;
            background-color: white;
            margin: 4px 0;
            transition: 0.4s;
        }

        @media only screen and (max-width: 768px) {
            nav ul li a {
                width: 200px;
                margin: 8px auto;
                text-align: center;
                display: block;
            }

            nav ul {
                padding: 15px 0;
            }
        }        nav .logo {
            height: 50px;
            width: auto;
            margin-right: 15px;
        }

        .mySlides {
            position: relative;
        }

        .text {
            position: absolute;
            bottom: 20px; /* Position at bottom */
            left: 20px; /* Position from left */
            right: auto; /* Remove right constraint */
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 8px 15px;
            text-align: left;
            font-size: 0.9rem;
            font-weight: 500;
            border-radius: 4px;
            max-width: 60%;
        }

        /* Responsive Styles */
        @media only screen and (max-width: 768px) {
            nav {
                flex-wrap: wrap;
            }

            nav .logo {
                height: 40px; /* Slightly smaller on mobile */
            }

            nav ul {
                display: none; /* Hide navigation links by default */
                flex-direction: column; /* Stack links vertically */
                width: 100%;
                text-align: center;
                background-color: #ffbf00;
            }

            nav ul.active {
                display: flex; /* Show links when activated */
            }

            .hamburger {
                display: flex; /* Show hamburger menu */
            }

            nav .heading {
                font-size: 1.2rem;
            }

            .text {
                font-size: 0.8rem;
                padding: 6px 12px;
                bottom: 15px;
                left: 15px;
            }
        }
        }
        /* Slideshow */
        .slideshow-container {
            position: relative;
            max-width: 100%;
            margin: auto;
            overflow: hidden;
        }

        .mySlides {
            display: none; /* Hide all slides initially */
        }
        .mySlides img {
            width: 100%;
            height: 400px; /* Further reduced height */
            object-fit: cover;
        }

        @media (max-width: 768px) {
            .mySlides img {
                height: auto;
            }
        }

        .prev, .next {
            cursor: pointer;
            position: absolute;
            top: 50%;
            padding: 16px;
            color: white;
            font-weight: bold;
            font-size: 18px;
            background-color: rgba(0,0,0,0.6);
            border-radius: 0 3px 3px 0;
            user-select: none;
            transition: 0.6s ease;
        }

        .next {
            right: 0;
            border-radius: 3px 0 0 3px;
        }

        /* Course Information Section */
        .about {
            padding: 20px;
        }

        .boxes {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            justify-content: center;
        }

        .box {
            background-color: #ffffff;
            border: 1px solid #ccc;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            width: calc(33% - 40px);
            text-align: center;
        }

        .box h2 {
            margin-bottom: 15px;
            font-size: 1.8rem;
        }

        .box p {
            margin: 0;
            font-size: 1.1rem;
            line-height: 1.6;
        }

        @media (max-width: 768px) {
            .mySlides img {
                height: auto;
            }

            .box {
                width: calc(100% - 40px); /* Full width on mobile */
            }

            .box h2 {
                font-size: 1.5rem;
            }

            .box p {
                font-size: 1rem;
            }
        }        /* Footer Section */
        footer {
            background-color: #333;
            color: white;
            text-align: center;
            padding: 15px 0;
            position: relative;
            bottom: 0;
            width: 100%;
        }

        footer a {
            color: #ffbf00;
            text-decoration: none;
        }

        footer a:hover {
            text-decoration: underline;
        }

        /* Introduction Section Styles */
        .intro-section {
            padding: 40px 5%;
            background-color: #f9f9f9;
            margin-bottom: 20px;
        }

        .intro-section .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .intro-section h2 {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
            font-size: 2rem;
            font-weight: 700;
        }

        .intro-content {
            background-color: #ffffff;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        }

        .intro-content p {
            margin-bottom: 20px;
            line-height: 1.6;
            font-size: 1.1rem;
            color: #444;
        }

        .intro-content ul {
            margin-bottom: 25px;
            padding-left: 20px;
        }

        .intro-content ul li {
            margin-bottom: 10px;
            line-height: 1.5;
            font-size: 1.05rem;
        }

        .intro-button-container {
            text-align: center;
            margin-top: 30px;
        }

        .intro-login-btn {
            display: inline-block;
            background-color: #ffbf00;
            color: #fff;
            font-size: 1.1rem;
            font-weight: 700;
            padding: 14px 30px;
            border-radius: 6px;
            text-decoration: none;
            transition: all 0.3s ease;
            text-transform: uppercase;
        }

        .intro-login-btn:hover {
            background-color: #cc9900;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        /* Mobile Responsiveness for Introduction Section */
        @media only screen and (max-width: 768px) {
            .intro-section {
                padding: 30px 15px;
            }

            .intro-section h2 {
                font-size: 1.6rem;
                margin-bottom: 20px;
            }

            .intro-content {
                padding: 20px;
            }

            .intro-content p {
                font-size: 1rem;
            }

            .intro-content ul li {
                font-size: 0.95rem;
            }

            .intro-login-btn {
                width: 100%;
                padding: 12px 20px;
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
<!-- Main Content Starts Here -->

<!-- Navigation Bar -->
<nav>
    <img src="assets/img/logo.webp" alt="DBTI Logo" class="logo">
    <div class="heading">DBTI Online Registration</div>
    <div class="hamburger" onclick="toggleNavbar()">
        <div></div>
        <div></div>
        <div></div>
    </div>
    <ul id="navbar">
        <li><a href="index.php">Home</a></li>
        <li><a href="about.php">About</a></li>
        <li><a href="../admin/admin_dashboard.php">Dashboard</a></li>
        <li><a href="login.php">Login</a></li>
    </ul>
</nav>

<!-- Slideshow Section -->
<div class="slider-container">
    <div class="slideshow-container fade-in">
        <div class="mySlides fade">
            <img src="assets/img/img3.png" alt="Slide 1">
            <div class="text">Main Offices</div>
        </div>
        <div class="mySlides fade">
            <img src="assets/img/qdg.png" alt="Slide 2">
            <div class="text">Campus Quadrangle</div>
        </div>
        <div class="mySlides fade">
            <img src="assets/img/mfw.png" alt="Slide 3">
            <div class="text">Metal Fabrication and Welding Technology Workshop</div>
        </div>
        <div class="mySlides fade">
            <img src="assets/img/aut.png" alt="Slide 4">
            <div class="text">Automotive Technology Workshop</div>
        </div>
        <div class="mySlides fade">
             <img src="assets/img/mfm.png" alt="Slide 5">
            <div class="text">Maintenance & Machine Fitting Technology Workshop</div>
        </div>
        <div class="mySlides fade">
            <img src="assets/img/ectelt.png" alt="Slide 6">
            <div class="text">Electronics & Electrical Technology Workshops</div>
        </div>
        <div class="mySlides fade">
            <img src="assets/img/inf.png" alt="Slide 7">
            <div class="text">Information Technology Lab</div>
        </div>
        <div class="mySlides fade">
            <img src="assets/img/ins.png" alt="Slide 8">
            <div class="text">Instrumentation Technology Workshop</div>
        </div>

        <!-- Navigation Arrows -->
        <a class="prev" onclick="plusSlides(-1)">❮</a>
        <a class="next" onclick="plusSlides(1)">❯</a>
    </div>
</div>

<!-- Introduction Section - After Slideshow and Before Course Information -->
<section class="intro-section">
    <div class="container">
        <h2>What is DBTI Online Registration and Payment System?</h2>
        <div class="intro-content">
            <p>The DBTI Online Registration and Payment System is a comprehensive digital platform designed to streamline the student registration process at Don Bosco Technological Institute. This system allows prospective and current students to register for courses, make tuition payments, and manage their academic information from anywhere, eliminating the need for in-person registration.</p>
            <p>Key features include:</p>
            <ul>
                <li>Easy course selection and registration</li>
                <li>Secure online payment options</li>
                <li>Real-time registration status tracking</li>
                <li>Digital document submission</li>
                <li>Student profile management</li>
            </ul>
            <div class="intro-button-container">
                <a href="login.php" class="intro-login-btn">Login to Get Started</a>
            </div>
        </div>
    </div>
</section>

<!-- Course Information Section -->
<section class="about" id="My Projects">
    <div class="content">
        <div class="boxes">
            <div class="box">
                <h2>DIPLOMA</h2>
                <p>The 3-year Diploma course at Don Bosco Technological Institute provides practical skills and theoretical knowledge in technical fields like IT, Engineering, and Automotive Technology. It emphasizes hands-on training, personal development, and professional discipline, preparing students for successful careers.</p>
            </div>
            <div class="box">
                <h2>BACHELOR</h2>
                <p>The Bachelor's degree course at Don Bosco Technological Institute is a comprehensive program offering in-depth knowledge and skills in fields such as Information Technology, Engineering, and Technical Education. Over four years, students engage in practical and theoretical learning, with a focus on innovation, problem-solving, and ethical leadership, preparing them for advanced careers or further studies in their chosen field.</p>
            </div>
            <div class="box">
                <h2>BACHELOR IN EDUCATION</h2>
                <p>The Bachelor's in Education degree at Don Bosco Technological Institute is a four-year program focused on training future educators in technical and vocational fields. It combines educational theory, teaching methods, and hands-on technical training, preparing graduates to become effective instructors in schools and training centers. The program emphasizes ethical values, professional development, and leadership skills essential for shaping the next generation.</p>
            </div>
        </div>
    </div>
</section>

<br>

<!-- Footer Section -->
<footer>
    <div class="footer">
        <span>
            Copyright &#169; 2024 Don Bosco Technological Institute. All Rights Reserved.
            <a href="https://www.dbti.ac.pg/" target="_blank">DBTI Website</a>
        </span>
    </div>
</footer>

<script>
    let slideIndex = 0;
    showSlides();

    function showSlides() {
        let slides = document.getElementsByClassName("mySlides");
        for (let i = 0; i < slides.length; i++) {
            slides[i].style.display = "none"; // Hide all slides
        }
        slideIndex++;
        if (slideIndex > slides.length) { slideIndex = 1; } // Loop back to first slide
        slides[slideIndex - 1].style.display = "block"; // Show the current slide
        setTimeout(showSlides, 2000); // Change slide every 2 seconds
    }

    function toggleNavbar() {
        const navbar = document.getElementById('navbar');
        navbar.classList.toggle('active'); // Toggle the active class
    }
</script>

</body>
</html>
