<?php
session_start();
require_once '../config/db_conn.php';

// Enable detailed error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Log all POST data for debugging
error_log("Registration POST data: " . print_r($_POST, true));

// Check if user is logged in
if (!isset($_SESSION['username']) || $_SESSION['role'] !== 'student') {
    error_log("User not logged in or not a student");
    $_SESSION['error'] = "You must be logged in as a student to register.";
    header("Location: login.php");
    exit();
}

// Validate form submission
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    error_log("Invalid request method: " . $_SERVER['REQUEST_METHOD']);
    $_SESSION['error'] = "Invalid request method";
    header("Location: registration.php");
    exit();
}

// Validate required fields
$required_fields = [
    'student_id', 'first_name', 'last_name', 'gender', 'phone_number',
    'residential_address', 'home_province', 'guardian_name', 'guardian_occupation',
    'guardian_phone_number', 'guardian_email', 'student_email', 'dob',
    'program', 'tech', 'year_level'
];

foreach ($required_fields as $field) {
    if (!isset($_POST[$field]) || empty($_POST[$field])) {
        error_log("Missing required field: $field");
        $_SESSION['error'] = "Missing required field: $field";
        header("Location: registration.php");
        exit();
    }
}

// Make sure courses were selected
if (!isset($_POST['courses']) || empty($_POST['courses'])) {
    error_log("No courses selected");
    $_SESSION['error'] = "Please select at least one course";
    header("Location: registration.php");
    exit();
}

// Get the selected courses
$selected_courses = $_POST['courses'];
$program = $_POST['program'] ?? '';
$tech = $_POST['tech'] ?? '';
$year_level = $_POST['year_level'] ?? '';

// Debug log
error_log("Selected program: $program, tech: $tech, year_level: $year_level");
error_log("Selected courses: " . implode(", ", $selected_courses));

// Get current date and time for registration timestamp
$registration_date = date('Y-m-d H:i:s');

// Get current semester and academic year
$current_month = date('n');
$semester = ($current_month >= 7 && $current_month <= 12) ? 'Semester 2' : 'Semester 1';
$academic_year = date('Y');

// Begin transaction
$conn->begin_transaction();

try {
    // Update student information with all fields
    $update_student_sql = "UPDATE students SET
        gender = ?,
        phone_number = ?,
        residential_address = ?,
        home_province = ?,
        guardian_name = ?,
        guardian_occupation = ?,
        guardian_phone_number = ?,
        guardian_email = ?,
        student_email = ?,
        dob = ?,
        program = ?,
        tech = ?,
        year_level = ?,
        registration_status = 'registered',
        registration_timestamp = NOW()
        WHERE student_id = ?";

    $stmt = $conn->prepare($update_student_sql);

    // Bind all parameters
    $stmt->bind_param("ssssssssssssss",
        $_POST['gender'],
        $_POST['phone_number'],
        $_POST['residential_address'],
        $_POST['home_province'],
        $_POST['guardian_name'],
        $_POST['guardian_occupation'],
        $_POST['guardian_phone_number'],
        $_POST['guardian_email'],
        $_POST['student_email'],
        $_POST['dob'],
        $_POST['program'],
        $_POST['tech'],
        $_POST['year_level'],
        $_POST['student_id']
    );

    // Execute the update
    if (!$stmt->execute()) {
        throw new Exception("Error updating student information: " . $stmt->error);
    }

    // Log successful update
    error_log("Successfully updated student information for student ID: " . $_POST['student_id']);

    // If everything is successful, commit the transaction
    $conn->commit();

    // Redirect to success page
    $_SESSION['message'] = "Registration completed successfully!";
    header("Location: student_dashboard.php");
    exit();

} catch (Exception $e) {
    // If there's an error, rollback the transaction
    $conn->rollback();
    error_log("Registration error: " . $e->getMessage());
    $_SESSION['error'] = "Registration failed: " . $e->getMessage();
    header("Location: registration.php");
    exit();
}
?>
