# DBTI Online Registration - URL Rewriting Rules
# This file ensures proper routing to the public directory

RewriteEngine On

# Redirect root requests to public directory
RewriteCond %{REQUEST_URI} !^/public/
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ public/$1 [L,QSA]

# Security: Block access to sensitive directories
RewriteRule ^(config|src|database|storage|scripts|vendor)/ - [F,L]

# Security: Block access to sensitive files
RewriteRule \.(env|sql|log)$ - [F,L]

# Security: Block access to backup files
RewriteRule \.(bak|backup|old|orig|save|swp|tmp)$ - [F,L]
