<?php
session_start();
require_once '../config/db_conn.php';

// No HTML or CSS should be here, only PHP code

// Check if user is logged in and has registrar role
if (!isset($_SESSION['username']) || $_SESSION['role'] !== 'registrar') {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit();
}

// Get current semester and academic year
$current_month = date('n');
$current_semester = ($current_month >= 7 && $current_month <= 12) ? 'Semester 2' : 'Semester 1';
$current_academic_year = date('Y');

// Begin transaction
$conn->begin_transaction();

try {
    // First check if there are any registered students to reset
    $check_sql = "SELECT COUNT(*) as count FROM students WHERE registration_status = 'registered'";
    $check_result = $conn->query($check_sql);
    $check_data = $check_result->fetch_assoc();
    
    if ($check_data['count'] == 0) {
        // No students to reset
        header('Content-Type: application/json');
        echo json_encode(['success' => true, 'message' => 'No students need to be reset.', 'count' => 0]);
        exit();
    }
    
    // First, mark all current registrations as "archived" in the registrations table
    // This is done by updating the status field if it exists
    $column_check = $conn->query("SHOW COLUMNS FROM registrations LIKE 'status'");
    if ($column_check->num_rows > 0) {
        $archive_sql = "UPDATE registrations 
                       SET status = 'archived' 
                       WHERE semester = ? AND academic_year = ? AND (status = 'current' OR status IS NULL)";
        $archive_stmt = $conn->prepare($archive_sql);
        $archive_stmt->bind_param("ss", $current_semester, $current_academic_year);
        $archive_stmt->execute();
    }
    
    // Then, reset all students' registration status to 'unregistered'
    $reset_sql = "UPDATE students SET registration_status = 'unregistered' WHERE registration_status = 'registered'";
    $reset_result = $conn->query($reset_sql);
    
    if ($reset_result) {
        // Get number of rows affected
        $affected_rows = $conn->affected_rows;
        
        // Commit transaction
        $conn->commit();
        
        header('Content-Type: application/json');
        echo json_encode([
            'success' => true, 
            'message' => 'Registration status reset successfully', 
            'count' => $affected_rows
        ]);
    } else {
        throw new Exception("Database update failed");
    }
} catch (Exception $e) {
    // Rollback transaction on error
    $conn->rollback();
    
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}
