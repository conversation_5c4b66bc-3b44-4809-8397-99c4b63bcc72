<?php
// Enhanced sync script to ensure complete synchronization of all elements
require_once '../config/db_conn.php';
include 'course_data.php'; // Include course data file

// Add error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Function to sync all course data to the database
function syncCoursesToDatabase($courses) {
    global $conn;
    
    try {
        // Check if courses table exists
        $table_check = $conn->query("SHOW TABLES LIKE 'courses'");
        if ($table_check->num_rows == 0) {
            // Create the courses table
            $create_table = "CREATE TABLE courses (
                id INT(11) AUTO_INCREMENT PRIMARY KEY,
                course_id VARCHAR(20) NOT NULL,
                course_name VARCHAR(255) NOT NULL,
                program VARCHAR(100) NOT NULL,
                technology VARCHAR(100) NOT NULL,
                year_level VARCHAR(50) NOT NULL,
                semester VARCHAR(20) NOT NULL DEFAULT 'Semester 1',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )";
            $conn->query($create_table);
            echo "<p>Courses table created successfully.</p>";
        }
        
        // Begin transaction for database consistency
        $conn->begin_transaction();
        
        // Clear existing courses
        $delete_result = $conn->query("DELETE FROM courses");
        $deleted_rows = $conn->affected_rows;
        echo "<p>Deleted $deleted_rows existing course records.</p>";
        
        // Track statistics for reporting
        $total_inserted = 0;
        $programs_synced = [];
        $technologies_synced = [];
        $years_synced = [];
        
        // Insert all courses
        foreach ($courses as $program => $technologies) {
            $programs_synced[$program] = true;
            
            foreach ($technologies as $tech => $years) {
                $technologies_synced[$tech] = true;
                
                foreach ($years as $year => $course_list) {
                    $years_synced[$year] = true;
                    
                    foreach ($course_list as $course_id => $course_name) {
                        // Determine semester based on course_id
                        $semester = (substr($course_id, -1) == '1') ? 'Semester 1' : 'Semester 2';
                        
                        $stmt = $conn->prepare("INSERT INTO courses (course_id, course_name, program, technology, year_level, semester) VALUES (?, ?, ?, ?, ?, ?)");
                        $stmt->bind_param("ssssss", $course_id, $course_name, $program, $tech, $year, $semester);
                        
                        if ($stmt->execute()) {
                            $total_inserted++;
                        } else {
                            echo "<p>Error inserting course $course_id: " . $conn->error . "</p>";
                        }
                    }
                }
            }
        }
        
        // Commit transaction
        $conn->commit();
        
        // Display sync results
        echo "<div style='background-color: #d4edda; color: #155724; padding: 15px; margin: 20px 0; border-radius: 4px;'>";
        echo "<h3>🎉 Synchronization Completed Successfully!</h3>";
        echo "<p>Successfully inserted $total_inserted courses into database.</p>";
        echo "<ul>";
        echo "<li><strong>Programs synced:</strong> " . count($programs_synced) . "</li>";
        echo "<li><strong>Technologies synced:</strong> " . count($technologies_synced) . "</li>";
        echo "<li><strong>Year levels synced:</strong> " . count($years_synced) . "</li>";
        echo "</ul>";
        echo "</div>";
        
        // Check what programs are in the database after sync
        $programs_result = $conn->query("SELECT DISTINCT program FROM courses");
        echo "<h3>Programs synced to database:</h3><ul>";
        while ($row = $programs_result->fetch_assoc()) {
            echo "<li>" . htmlspecialchars($row['program']) . "</li>";
        }
        echo "</ul>";

        // Check technologies
        $techs_result = $conn->query("SELECT DISTINCT technology FROM courses");
        echo "<h3>Technologies synced to database:</h3><ul>";
        while ($row = $techs_result->fetch_assoc()) {
            echo "<li>" . htmlspecialchars($row['technology']) . "</li>";
        }
        echo "</ul>";
        
        return true;
    } catch (Exception $e) {
        // Roll back transaction on error
        $conn->rollback();
        echo "<div style='background-color: #f8d7da; color: #721c24; padding: 15px; margin: 20px 0; border-radius: 4px;'>";
        echo "<h3>❌ Synchronization Failed</h3>";
        echo "<p>Error during sync: " . $e->getMessage() . "</p>";
        echo "</div>";
        return false;
    }
}

// Verification function to check if all programs and technologies are synced
function verifySync($courses) {
    global $conn;
    
    $db_programs = [];
    $db_technologies = [];
    $file_programs = array_keys($courses);
    $file_technologies = [];
    
    // Get all technologies from file
    foreach ($courses as $program => $technologies) {
        foreach (array_keys($technologies) as $tech) {
            $file_technologies[] = $tech;
        }
    }
    $file_technologies = array_unique($file_technologies);
    
    // Get from database
    $programs_result = $conn->query("SELECT DISTINCT program FROM courses");
    while ($row = $programs_result->fetch_assoc()) {
        $db_programs[] = $row['program'];
    }
    
    $techs_result = $conn->query("SELECT DISTINCT technology FROM courses");
    while ($row = $techs_result->fetch_assoc()) {
        $db_technologies[] = $row['technology'];
    }
    
    // Check for missing programs
    $missing_programs = array_diff($file_programs, $db_programs);
    $missing_techs = array_diff($file_technologies, $db_technologies);
    
    if (!empty($missing_programs) || !empty($missing_techs)) {
        echo "<div style='background-color: #fff3cd; color: #856404; padding: 15px; margin: 20px 0; border-radius: 4px;'>";
        echo "<h3>⚠️ Synchronization Warning</h3>";
        
        if (!empty($missing_programs)) {
            echo "<p><strong>Missing programs:</strong> " . implode(", ", $missing_programs) . "</p>";
        }
        
        if (!empty($missing_techs)) {
            echo "<p><strong>Missing technologies:</strong> " . implode(", ", $missing_techs) . "</p>";
        }
        
        echo "<p>To fix these issues, please run the sync again.</p>";
        echo "</div>";
        
        return false;
    }
    
    return true;
}

// Execute sync
echo "<h1>Course Synchronization</h1>";
$sync_success = syncCoursesToDatabase($courses);

if ($sync_success) {
    // Verify the sync was complete
    verifySync($courses);
}
?>

<div style="margin-top: 30px;">
    <a href="check_courses.php" style="padding: 10px 20px; background-color: #007bff; color: white; text-decoration: none; border-radius: 4px; margin-right: 10px;">
        Check Course Database
    </a>
    <a href="registrar_dashboard.php" style="padding: 10px 20px; background-color: #6c757d; color: white; text-decoration: none; border-radius: 4px;">
        Return to Dashboard
    </a>
</div> 