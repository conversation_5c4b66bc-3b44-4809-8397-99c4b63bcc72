<?php
session_start();
require_once '../config/db_conn.php';

// Check if user is logged in as registrar
if (!isset($_SESSION['username']) || $_SESSION['role'] !== 'registrar') {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit();
}

// Check if this is a POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit();
}

// Get JSON data
$data = json_decode(file_get_contents('php://input'), true);

if (isset($data['action']) && $data['action'] === 'reset_semester') {
    // Get current semester and academic year
    $current_month = date('n');
    $current_semester = ($current_month >= 7 && $current_month <= 12) ? 'Semester 2' : 'Semester 1';
    $current_academic_year = date('Y');
    
    // Begin transaction
    $conn->begin_transaction();
    
    try {
        // Update all current registrations to have status = 'previous'
        $update_sql = "UPDATE registrations 
                      SET status = 'previous' 
                      WHERE semester = ? AND academic_year = ? AND status = 'current'";
        
        $stmt = $conn->prepare($update_sql);
        $stmt->bind_param("ss", $current_semester, $current_academic_year);
        $stmt->execute();
        
        // Also update students table to mark them as unregistered
        $update_students_sql = "UPDATE students s
                               INNER JOIN registrations r ON s.student_id = r.student_id
                               SET s.registration_status = 'unregistered'
                               WHERE r.semester = ? AND r.academic_year = ? AND r.status = 'previous'";
        
        $stmt2 = $conn->prepare($update_students_sql);
        $stmt2->bind_param("ss", $current_semester, $current_academic_year);
        $stmt2->execute();
        
        // Commit transaction
        $conn->commit();
        
        // Return success response
        header('Content-Type: application/json');
        echo json_encode(['success' => true, 'message' => 'Semester reset successful']);
    } catch (Exception $e) {
        // Rollback transaction on error
        $conn->rollback();
        
        // Log the error
        error_log("Semester reset error: " . $e->getMessage());
        
        // Return error response
        header('Content-Type: application/json');
        echo json_encode(['success' => false, 'message' => 'Database error occurred']);
    }
} else {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Invalid action']);
}
?>
