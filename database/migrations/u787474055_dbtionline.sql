-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1:3306
-- Generation Time: May 22, 2025 at 01:24 AM
-- Server version: 10.11.10-MariaDB-log
-- PHP Version: 7.2.34

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `u787474055_dbtionline`
--

-- --------------------------------------------------------

--
-- Table structure for table `courses`
--

CREATE TABLE `courses` (
  `id` int(11) NOT NULL,
  `course_id` varchar(20) NOT NULL,
  `course_name` varchar(255) NOT NULL,
  `program` varchar(100) NOT NULL,
  `technology` varchar(100) NOT NULL,
  `year_level` varchar(50) NOT NULL,
  `semester` varchar(20) NOT NULL DEFAULT 'Semester 1',
  `created_at` timestamp NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `courses`
--

INSERT INTO `courses` (`id`, `course_id`, `course_name`, `program`, `technology`, `year_level`, `semester`, `created_at`) VALUES
(2591, 'INF121', 'Introduction to Programming (3 units)', 'Bachelor in Technology', 'Information Technology', 'Year 1', 'Semester 1', '2025-03-23 13:44:51'),
(2592, 'INF122', 'Computer Fundamentals (3 units)', 'Bachelor in Technology', 'Information Technology', 'Year 1', 'Semester 2', '2025-03-23 13:44:51'),
(2593, 'PTR121', 'Basic Workshop Practice (3 units)', 'Bachelor in Technology', 'Information Technology', 'Year 1', 'Semester 1', '2025-03-23 13:44:51'),
(2594, 'PTR122', 'Technical Drawing (3 units)', 'Bachelor in Technology', 'Information Technology', 'Year 1', 'Semester 2', '2025-03-23 13:44:51'),
(2595, 'THE121', 'Theology 1 (3 units)', 'Bachelor in Technology', 'Information Technology', 'Year 1', 'Semester 1', '2025-03-23 13:44:51'),
(2596, 'THE122', 'Theology 2 (3 units)', 'Bachelor in Technology', 'Information Technology', 'Year 1', 'Semester 2', '2025-03-23 13:44:51'),
(2597, 'INF221', 'Database Systems (3 units)', 'Bachelor in Technology', 'Information Technology', 'Year 2', 'Semester 1', '2025-03-23 13:44:51'),
(2598, 'INF222', 'Web Development (3 units)', 'Bachelor in Technology', 'Information Technology', 'Year 2', 'Semester 2', '2025-03-23 13:44:51'),
(2599, 'PTR221', 'Professional Practice (3 units)', 'Bachelor in Technology', 'Information Technology', 'Year 2', 'Semester 1', '2025-03-23 13:44:51'),
(2600, 'PTR222', 'Technical Writing (3 units)', 'Bachelor in Technology', 'Information Technology', 'Year 2', 'Semester 2', '2025-03-23 13:44:51'),
(2601, 'THE221', 'Theology 3 (3 units)', 'Bachelor in Technology', 'Information Technology', 'Year 2', 'Semester 1', '2025-03-23 13:44:51'),
(2602, 'THE222', 'Theology 4 (3 units)', 'Bachelor in Technology', 'Information Technology', 'Year 2', 'Semester 2', '2025-03-23 13:44:51'),
(2603, 'INF321', 'Software Engineering (3 units)', 'Bachelor in Technology', 'Information Technology', 'Year 3', 'Semester 1', '2025-03-23 13:44:51'),
(2604, 'INF322', 'Network Administration (3 units)', 'Bachelor in Technology', 'Information Technology', 'Year 3', 'Semester 2', '2025-03-23 13:44:51'),
(2605, 'PTR321', 'Industrial Relations (3 units)', 'Bachelor in Technology', 'Information Technology', 'Year 3', 'Semester 1', '2025-03-23 13:44:51'),
(2606, 'PTR322', 'Project Management (3 units)', 'Bachelor in Technology', 'Information Technology', 'Year 3', 'Semester 2', '2025-03-23 13:44:51'),
(2607, 'THE321', 'Theology 5 (3 units)', 'Bachelor in Technology', 'Information Technology', 'Year 3', 'Semester 1', '2025-03-23 13:44:51'),
(2608, 'THE322', 'Theology 6 (3 units)', 'Bachelor in Technology', 'Information Technology', 'Year 3', 'Semester 2', '2025-03-23 13:44:51'),
(2609, 'INF421', 'Wide Area Network (3 units)', 'Bachelor in Technology', 'Information Technology', 'Year 4', 'Semester 1', '2025-03-23 13:44:51'),
(2610, 'INF422', 'Electronic Commerce (3 units)', 'Bachelor in Technology', 'Information Technology', 'Year 4', 'Semester 2', '2025-03-23 13:44:51'),
(2611, 'PTR421', 'Supervision in Workplace (3 units)', 'Bachelor in Technology', 'Information Technology', 'Year 4', 'Semester 1', '2025-03-23 13:44:51'),
(2612, 'PTR422', 'Project Study (3 units)', 'Bachelor in Technology', 'Information Technology', 'Year 4', 'Semester 2', '2025-03-23 13:44:51'),
(2613, 'THE421', 'Theology 7 (3 units)', 'Bachelor in Technology', 'Information Technology', 'Year 4', 'Semester 1', '2025-03-23 13:44:51'),
(2614, 'THE422', 'Theology 8 (3 units)', 'Bachelor in Technology', 'Information Technology', 'Year 4', 'Semester 2', '2025-03-23 13:44:51'),
(2615, 'AUT121', 'Basic Automotive Systems (3 units)', 'Bachelor in Technology', 'Automotive Technology', 'Year 1', 'Semester 1', '2025-03-23 13:44:51'),
(2616, 'AUT122', 'Automotive Tools and Equipment (3 units)', 'Bachelor in Technology', 'Automotive Technology', 'Year 1', 'Semester 2', '2025-03-23 13:44:51'),
(2617, 'PTR121', 'Basic Workshop Practice (3 units)', 'Bachelor in Technology', 'Automotive Technology', 'Year 1', 'Semester 1', '2025-03-23 13:44:51'),
(2618, 'PTR122', 'Technical Drawing (3 units)', 'Bachelor in Technology', 'Automotive Technology', 'Year 1', 'Semester 2', '2025-03-23 13:44:51'),
(2619, 'THE121', 'Theology 1 (3 units)', 'Bachelor in Technology', 'Automotive Technology', 'Year 1', 'Semester 1', '2025-03-23 13:44:51'),
(2620, 'THE122', 'Theology 2 (3 units)', 'Bachelor in Technology', 'Automotive Technology', 'Year 1', 'Semester 2', '2025-03-23 13:44:51'),
(2621, 'AUT221', 'Engine Systems (3 units)', 'Bachelor in Technology', 'Automotive Technology', 'Year 2', 'Semester 1', '2025-03-23 13:44:51'),
(2622, 'AUT222', 'Electrical Systems (3 units)', 'Bachelor in Technology', 'Automotive Technology', 'Year 2', 'Semester 2', '2025-03-23 13:44:51'),
(2623, 'PTR221', 'Professional Practice (3 units)', 'Bachelor in Technology', 'Automotive Technology', 'Year 2', 'Semester 1', '2025-03-23 13:44:51'),
(2624, 'PTR222', 'Technical Writing (3 units)', 'Bachelor in Technology', 'Automotive Technology', 'Year 2', 'Semester 2', '2025-03-23 13:44:51'),
(2625, 'THE221', 'Theology 3 (3 units)', 'Bachelor in Technology', 'Automotive Technology', 'Year 2', 'Semester 1', '2025-03-23 13:44:51'),
(2626, 'THE222', 'Theology 4 (3 units)', 'Bachelor in Technology', 'Automotive Technology', 'Year 2', 'Semester 2', '2025-03-23 13:44:51'),
(2627, 'AUT321', 'Automotive Systems (3 units)', 'Bachelor in Technology', 'Automotive Technology', 'Year 3', 'Semester 1', '2025-03-23 13:44:51'),
(2628, 'AUT322', 'Vehicle Maintenance (3 units)', 'Bachelor in Technology', 'Automotive Technology', 'Year 3', 'Semester 2', '2025-03-23 13:44:51'),
(2629, 'PTR321', 'Industrial Relations (3 units)', 'Bachelor in Technology', 'Automotive Technology', 'Year 3', 'Semester 1', '2025-03-23 13:44:51'),
(2630, 'PTR322', 'Project Management (3 units)', 'Bachelor in Technology', 'Automotive Technology', 'Year 3', 'Semester 2', '2025-03-23 13:44:51'),
(2631, 'THE321', 'Theology 5 (3 units)', 'Bachelor in Technology', 'Automotive Technology', 'Year 3', 'Semester 1', '2025-03-23 13:44:51'),
(2632, 'THE322', 'Theology 6 (3 units)', 'Bachelor in Technology', 'Automotive Technology', 'Year 3', 'Semester 2', '2025-03-23 13:44:51'),
(2633, 'AUT421', 'Advanced Automotive Systems (3 units)', 'Bachelor in Technology', 'Automotive Technology', 'Year 4', 'Semester 1', '2025-03-23 13:44:51'),
(2634, 'AUT422', 'Vehicle Diagnostics (3 units)', 'Bachelor in Technology', 'Automotive Technology', 'Year 4', 'Semester 2', '2025-03-23 13:44:51'),
(2635, 'PTR421', 'Supervision in Workplace (3 units)', 'Bachelor in Technology', 'Automotive Technology', 'Year 4', 'Semester 1', '2025-03-23 13:44:51'),
(2636, 'PTR422', 'Project Study (3 units)', 'Bachelor in Technology', 'Automotive Technology', 'Year 4', 'Semester 2', '2025-03-23 13:44:51'),
(2637, 'THE421', 'Theology 7 (3 units)', 'Bachelor in Technology', 'Automotive Technology', 'Year 4', 'Semester 1', '2025-03-23 13:44:51'),
(2638, 'THE422', 'Theology 8 (3 units)', 'Bachelor in Technology', 'Automotive Technology', 'Year 4', 'Semester 2', '2025-03-23 13:44:51'),
(2639, 'INS121', 'Basic Instrumentation (3 units)', 'Bachelor in Technology', 'Instrumentation Technology', 'Year 1', 'Semester 1', '2025-03-23 13:44:51'),
(2640, 'INS122', 'Instrumentation Tools (3 units)', 'Bachelor in Technology', 'Instrumentation Technology', 'Year 1', 'Semester 2', '2025-03-23 13:44:51'),
(2641, 'PTR121', 'Basic Workshop Practice (3 units)', 'Bachelor in Technology', 'Instrumentation Technology', 'Year 1', 'Semester 1', '2025-03-23 13:44:51'),
(2642, 'PTR122', 'Technical Drawing (3 units)', 'Bachelor in Technology', 'Instrumentation Technology', 'Year 1', 'Semester 2', '2025-03-23 13:44:51'),
(2643, 'THE121', 'Theology 1 (3 units)', 'Bachelor in Technology', 'Instrumentation Technology', 'Year 1', 'Semester 1', '2025-03-23 13:44:51'),
(2644, 'THE122', 'Theology 2 (3 units)', 'Bachelor in Technology', 'Instrumentation Technology', 'Year 1', 'Semester 2', '2025-03-23 13:44:51'),
(2645, 'INS221', 'Instrumentation Systems (3 units)', 'Bachelor in Technology', 'Instrumentation Technology', 'Year 2', 'Semester 1', '2025-03-23 13:44:51'),
(2646, 'INS222', 'Measurement Techniques (3 units)', 'Bachelor in Technology', 'Instrumentation Technology', 'Year 2', 'Semester 2', '2025-03-23 13:44:51'),
(2647, 'PTR221', 'Professional Practice (3 units)', 'Bachelor in Technology', 'Instrumentation Technology', 'Year 2', 'Semester 1', '2025-03-23 13:44:51'),
(2648, 'PTR222', 'Technical Writing (3 units)', 'Bachelor in Technology', 'Instrumentation Technology', 'Year 2', 'Semester 2', '2025-03-23 13:44:51'),
(2649, 'THE221', 'Theology 3 (3 units)', 'Bachelor in Technology', 'Instrumentation Technology', 'Year 2', 'Semester 1', '2025-03-23 13:44:51'),
(2650, 'THE222', 'Theology 4 (3 units)', 'Bachelor in Technology', 'Instrumentation Technology', 'Year 2', 'Semester 2', '2025-03-23 13:44:51'),
(2651, 'INS321', 'Advanced Measurement (3 units)', 'Bachelor in Technology', 'Instrumentation Technology', 'Year 3', 'Semester 1', '2025-03-23 13:44:51'),
(2652, 'INS322', 'Control Systems (3 units)', 'Bachelor in Technology', 'Instrumentation Technology', 'Year 3', 'Semester 2', '2025-03-23 13:44:51'),
(2653, 'PTR321', 'Industrial Relations (3 units)', 'Bachelor in Technology', 'Instrumentation Technology', 'Year 3', 'Semester 1', '2025-03-23 13:44:51'),
(2654, 'PTR322', 'Project Management (3 units)', 'Bachelor in Technology', 'Instrumentation Technology', 'Year 3', 'Semester 2', '2025-03-23 13:44:51'),
(2655, 'THE321', 'Theology 5 (3 units)', 'Bachelor in Technology', 'Instrumentation Technology', 'Year 3', 'Semester 1', '2025-03-23 13:44:51'),
(2656, 'THE322', 'Theology 6 (3 units)', 'Bachelor in Technology', 'Instrumentation Technology', 'Year 3', 'Semester 2', '2025-03-23 13:44:51'),
(2657, 'INS421', 'Process Control Systems (3 units)', 'Bachelor in Technology', 'Instrumentation Technology', 'Year 4', 'Semester 1', '2025-03-23 13:44:51'),
(2658, 'INS422', 'Instrumentation Project (3 units)', 'Bachelor in Technology', 'Instrumentation Technology', 'Year 4', 'Semester 2', '2025-03-23 13:44:51'),
(2659, 'PTR421', 'Supervision in Workplace (3 units)', 'Bachelor in Technology', 'Instrumentation Technology', 'Year 4', 'Semester 1', '2025-03-23 13:44:51'),
(2660, 'PTR422', 'Project Study (3 units)', 'Bachelor in Technology', 'Instrumentation Technology', 'Year 4', 'Semester 2', '2025-03-23 13:44:51'),
(2661, 'THE421', 'Theology 7 (3 units)', 'Bachelor in Technology', 'Instrumentation Technology', 'Year 4', 'Semester 1', '2025-03-23 13:44:51'),
(2662, 'THE422', 'Theology 8 (3 units)', 'Bachelor in Technology', 'Instrumentation Technology', 'Year 4', 'Semester 2', '2025-03-23 13:44:51'),
(2663, 'ELE121', 'Electronic Circuits (3 units)', 'Bachelor in Technology', 'Electronics Technology', 'Year 1', 'Semester 1', '2025-03-23 13:44:51'),
(2664, 'ELE122', 'Digital Electronics (3 units)', 'Bachelor in Technology', 'Electronics Technology', 'Year 1', 'Semester 2', '2025-03-23 13:44:51'),
(2665, 'PTR121', 'Basic Workshop Practice (3 units)', 'Bachelor in Technology', 'Electronics Technology', 'Year 1', 'Semester 1', '2025-03-23 13:44:51'),
(2666, 'PTR122', 'Technical Drawing (3 units)', 'Bachelor in Technology', 'Electronics Technology', 'Year 1', 'Semester 2', '2025-03-23 13:44:51'),
(2667, 'THE121', 'Theology 1 (3 units)', 'Bachelor in Technology', 'Electronics Technology', 'Year 1', 'Semester 1', '2025-03-23 13:44:51'),
(2668, 'THE122', 'Theology 2 (3 units)', 'Bachelor in Technology', 'Electronics Technology', 'Year 1', 'Semester 2', '2025-03-23 13:44:51'),
(2669, 'ELE221', 'Microcontroller Programming (3 units)', 'Bachelor in Technology', 'Electronics Technology', 'Year 2', 'Semester 1', '2025-03-23 13:44:51'),
(2670, 'ELE222', 'Analog Electronics (3 units)', 'Bachelor in Technology', 'Electronics Technology', 'Year 2', 'Semester 2', '2025-03-23 13:44:51'),
(2671, 'PTR221', 'Professional Practice (3 units)', 'Bachelor in Technology', 'Electronics Technology', 'Year 2', 'Semester 1', '2025-03-23 13:44:51'),
(2672, 'PTR222', 'Technical Writing (3 units)', 'Bachelor in Technology', 'Electronics Technology', 'Year 2', 'Semester 2', '2025-03-23 13:44:51'),
(2673, 'THE221', 'Theology 3 (3 units)', 'Bachelor in Technology', 'Electronics Technology', 'Year 2', 'Semester 1', '2025-03-23 13:44:51'),
(2674, 'THE222', 'Theology 4 (3 units)', 'Bachelor in Technology', 'Electronics Technology', 'Year 2', 'Semester 2', '2025-03-23 13:44:51'),
(2675, 'ELE321', 'Advanced Electronics (3 units)', 'Bachelor in Technology', 'Electronics Technology', 'Year 3', 'Semester 1', '2025-03-23 13:44:51'),
(2676, 'ELE322', 'Communication Systems (3 units)', 'Bachelor in Technology', 'Electronics Technology', 'Year 3', 'Semester 2', '2025-03-23 13:44:51'),
(2677, 'PTR321', 'Industrial Relations (3 units)', 'Bachelor in Technology', 'Electronics Technology', 'Year 3', 'Semester 1', '2025-03-23 13:44:51'),
(2678, 'PTR322', 'Project Management (3 units)', 'Bachelor in Technology', 'Electronics Technology', 'Year 3', 'Semester 2', '2025-03-23 13:44:51'),
(2679, 'THE321', 'Theology 5 (3 units)', 'Bachelor in Technology', 'Electronics Technology', 'Year 3', 'Semester 1', '2025-03-23 13:44:51'),
(2680, 'THE322', 'Theology 6 (3 units)', 'Bachelor in Technology', 'Electronics Technology', 'Year 3', 'Semester 2', '2025-03-23 13:44:51'),
(2681, 'ELE421', 'Embedded Systems (3 units)', 'Bachelor in Technology', 'Electronics Technology', 'Year 4', 'Semester 1', '2025-03-23 13:44:51'),
(2682, 'ELE422', 'Capstone Project (3 units)', 'Bachelor in Technology', 'Electronics Technology', 'Year 4', 'Semester 2', '2025-03-23 13:44:51'),
(2683, 'PTR421', 'Supervision in Workplace (3 units)', 'Bachelor in Technology', 'Electronics Technology', 'Year 4', 'Semester 1', '2025-03-23 13:44:51'),
(2684, 'PTR422', 'Project Study (3 units)', 'Bachelor in Technology', 'Electronics Technology', 'Year 4', 'Semester 2', '2025-03-23 13:44:51'),
(2685, 'THE421', 'Theology 7 (3 units)', 'Bachelor in Technology', 'Electronics Technology', 'Year 4', 'Semester 1', '2025-03-23 13:44:51'),
(2686, 'THE422', 'Theology 8 (3 units)', 'Bachelor in Technology', 'Electronics Technology', 'Year 4', 'Semester 2', '2025-03-23 13:44:51'),
(2687, 'ETL121', 'Electrical Basics (3 units)', 'Bachelor in Technology', 'Electrical Technology', 'Year 1', 'Semester 1', '2025-03-23 13:44:51'),
(2688, 'ETL122', 'Circuit Analysis (3 units)', 'Bachelor in Technology', 'Electrical Technology', 'Year 1', 'Semester 2', '2025-03-23 13:44:51'),
(2689, 'PTR121', 'Basic Workshop Practice (3 units)', 'Bachelor in Technology', 'Electrical Technology', 'Year 1', 'Semester 1', '2025-03-23 13:44:51'),
(2690, 'PTR122', 'Technical Drawing (3 units)', 'Bachelor in Technology', 'Electrical Technology', 'Year 1', 'Semester 2', '2025-03-23 13:44:51'),
(2691, 'THE121', 'Theology 1 (3 units)', 'Bachelor in Technology', 'Electrical Technology', 'Year 1', 'Semester 1', '2025-03-23 13:44:51'),
(2692, 'THE122', 'Theology 2 (3 units)', 'Bachelor in Technology', 'Electrical Technology', 'Year 1', 'Semester 2', '2025-03-23 13:44:51'),
(2693, 'ETL221', 'Power Systems (3 units)', 'Bachelor in Technology', 'Electrical Technology', 'Year 2', 'Semester 1', '2025-03-23 13:44:51'),
(2694, 'ETL222', 'Control Systems (3 units)', 'Bachelor in Technology', 'Electrical Technology', 'Year 2', 'Semester 2', '2025-03-23 13:44:51'),
(2695, 'PTR221', 'Professional Practice (3 units)', 'Bachelor in Technology', 'Electrical Technology', 'Year 2', 'Semester 1', '2025-03-23 13:44:51'),
(2696, 'PTR222', 'Technical Writing (3 units)', 'Bachelor in Technology', 'Electrical Technology', 'Year 2', 'Semester 2', '2025-03-23 13:44:51'),
(2697, 'THE221', 'Theology 3 (3 units)', 'Bachelor in Technology', 'Electrical Technology', 'Year 2', 'Semester 1', '2025-03-23 13:44:51'),
(2698, 'THE222', 'Theology 4 (3 units)', 'Bachelor in Technology', 'Electrical Technology', 'Year 2', 'Semester 2', '2025-03-23 13:44:51'),
(2699, 'ETL321', 'Electrical Machines (3 units)', 'Bachelor in Technology', 'Electrical Technology', 'Year 3', 'Semester 1', '2025-03-23 13:44:51'),
(2700, 'ETL322', 'Industrial Electronics (3 units)', 'Bachelor in Technology', 'Electrical Technology', 'Year 3', 'Semester 2', '2025-03-23 13:44:51'),
(2701, 'PTR321', 'Industrial Relations (3 units)', 'Bachelor in Technology', 'Electrical Technology', 'Year 3', 'Semester 1', '2025-03-23 13:44:51'),
(2702, 'PTR322', 'Project Management (3 units)', 'Bachelor in Technology', 'Electrical Technology', 'Year 3', 'Semester 2', '2025-03-23 13:44:51'),
(2703, 'THE321', 'Theology 5 (3 units)', 'Bachelor in Technology', 'Electrical Technology', 'Year 3', 'Semester 1', '2025-03-23 13:44:51'),
(2704, 'THE322', 'Theology 6 (3 units)', 'Bachelor in Technology', 'Electrical Technology', 'Year 3', 'Semester 2', '2025-03-23 13:44:51'),
(2705, 'ETL421', 'Advanced Electrical Systems (3 units)', 'Bachelor in Technology', 'Electrical Technology', 'Year 4', 'Semester 1', '2025-03-23 13:44:51'),
(2706, 'ETL422', 'Capstone Project (3 units)', 'Bachelor in Technology', 'Electrical Technology', 'Year 4', 'Semester 2', '2025-03-23 13:44:51'),
(2707, 'PTR421', 'Supervision in Workplace (3 units)', 'Bachelor in Technology', 'Electrical Technology', 'Year 4', 'Semester 1', '2025-03-23 13:44:51'),
(2708, 'PTR422', 'Project Study (3 units)', 'Bachelor in Technology', 'Electrical Technology', 'Year 4', 'Semester 2', '2025-03-23 13:44:51'),
(2709, 'THE421', 'Theology 7 (3 units)', 'Bachelor in Technology', 'Electrical Technology', 'Year 4', 'Semester 1', '2025-03-23 13:44:51'),
(2710, 'THE422', 'Theology 8 (3 units)', 'Bachelor in Technology', 'Electrical Technology', 'Year 4', 'Semester 2', '2025-03-23 13:44:51'),
(2711, 'MWF121', 'Introduction to Welding (3 units)', 'Bachelor in Technology', 'Metal Welding & Fabrication Technology', 'Year 1', 'Semester 1', '2025-03-23 13:44:51'),
(2712, 'MWF122', 'Fabrication Basics (3 units)', 'Bachelor in Technology', 'Metal Welding & Fabrication Technology', 'Year 1', 'Semester 2', '2025-03-23 13:44:51'),
(2713, 'PTR121', 'Basic Workshop Practice (3 units)', 'Bachelor in Technology', 'Metal Welding & Fabrication Technology', 'Year 1', 'Semester 1', '2025-03-23 13:44:51'),
(2714, 'PTR122', 'Technical Drawing (3 units)', 'Bachelor in Technology', 'Metal Welding & Fabrication Technology', 'Year 1', 'Semester 2', '2025-03-23 13:44:51'),
(2715, 'THE121', 'Theology 1 (3 units)', 'Bachelor in Technology', 'Metal Welding & Fabrication Technology', 'Year 1', 'Semester 1', '2025-03-23 13:44:51'),
(2716, 'THE122', 'Theology 2 (3 units)', 'Bachelor in Technology', 'Metal Welding & Fabrication Technology', 'Year 1', 'Semester 2', '2025-03-23 13:44:51'),
(2717, 'MWF221', 'Advanced Welding (3 units)', 'Bachelor in Technology', 'Metal Welding & Fabrication Technology', 'Year 2', 'Semester 1', '2025-03-23 13:44:51'),
(2718, 'MWF222', 'Metal Fabrication Techniques (3 units)', 'Bachelor in Technology', 'Metal Welding & Fabrication Technology', 'Year 2', 'Semester 2', '2025-03-23 13:44:51'),
(2719, 'PTR221', 'Professional Practice (3 units)', 'Bachelor in Technology', 'Metal Welding & Fabrication Technology', 'Year 2', 'Semester 1', '2025-03-23 13:44:51'),
(2720, 'PTR222', 'Technical Writing (3 units)', 'Bachelor in Technology', 'Metal Welding & Fabrication Technology', 'Year 2', 'Semester 2', '2025-03-23 13:44:51'),
(2721, 'THE221', 'Theology 3 (3 units)', 'Bachelor in Technology', 'Metal Welding & Fabrication Technology', 'Year 2', 'Semester 1', '2025-03-23 13:44:51'),
(2722, 'THE222', 'Theology 4 (3 units)', 'Bachelor in Technology', 'Metal Welding & Fabrication Technology', 'Year 2', 'Semester 2', '2025-03-23 13:44:51'),
(2723, 'MWF321', 'Welding Systems (3 units)', 'Bachelor in Technology', 'Metal Welding & Fabrication Technology', 'Year 3', 'Semester 1', '2025-03-23 13:44:51'),
(2724, 'MWF322', 'Fabrication Project (3 units)', 'Bachelor in Technology', 'Metal Welding & Fabrication Technology', 'Year 3', 'Semester 2', '2025-03-23 13:44:51'),
(2725, 'PTR321', 'Industrial Relations (3 units)', 'Bachelor in Technology', 'Metal Welding & Fabrication Technology', 'Year 3', 'Semester 1', '2025-03-23 13:44:51'),
(2726, 'PTR322', 'Project Management (3 units)', 'Bachelor in Technology', 'Metal Welding & Fabrication Technology', 'Year 3', 'Semester 2', '2025-03-23 13:44:51'),
(2727, 'THE321', 'Theology 5 (3 units)', 'Bachelor in Technology', 'Metal Welding & Fabrication Technology', 'Year 3', 'Semester 1', '2025-03-23 13:44:51'),
(2728, 'THE322', 'Theology 6 (3 units)', 'Bachelor in Technology', 'Metal Welding & Fabrication Technology', 'Year 3', 'Semester 2', '2025-03-23 13:44:51'),
(2729, 'MWF421', 'Advanced Welding Processes (3 units)', 'Bachelor in Technology', 'Metal Welding & Fabrication Technology', 'Year 4', 'Semester 1', '2025-03-23 13:44:51'),
(2730, 'MWF422', 'Capstone Fabrication Project (3 units)', 'Bachelor in Technology', 'Metal Welding & Fabrication Technology', 'Year 4', 'Semester 2', '2025-03-23 13:44:51'),
(2731, 'PTR421', 'Supervision in Workplace (3 units)', 'Bachelor in Technology', 'Metal Welding & Fabrication Technology', 'Year 4', 'Semester 1', '2025-03-23 13:44:51'),
(2732, 'PTR422', 'Project Study (3 units)', 'Bachelor in Technology', 'Metal Welding & Fabrication Technology', 'Year 4', 'Semester 2', '2025-03-23 13:44:51'),
(2733, 'THE421', 'Theology 7 (3 units)', 'Bachelor in Technology', 'Metal Welding & Fabrication Technology', 'Year 4', 'Semester 1', '2025-03-23 13:44:51'),
(2734, 'THE422', 'Theology 8 (3 units)', 'Bachelor in Technology', 'Metal Welding & Fabrication Technology', 'Year 4', 'Semester 2', '2025-03-23 13:44:51'),
(2735, 'MFM121', 'Machining Basics (3 units)', 'Bachelor in Technology', 'Maintenance Fitting & Machining Technology', 'Year 1', 'Semester 1', '2025-03-23 13:44:51'),
(2736, 'MFM122', 'Basic Fitting Techniques (3 units)', 'Bachelor in Technology', 'Maintenance Fitting & Machining Technology', 'Year 1', 'Semester 2', '2025-03-23 13:44:51'),
(2737, 'PTR121', 'Basic Workshop Practice (3 units)', 'Bachelor in Technology', 'Maintenance Fitting & Machining Technology', 'Year 1', 'Semester 1', '2025-03-23 13:44:51'),
(2738, 'PTR122', 'Technical Drawing (3 units)', 'Bachelor in Technology', 'Maintenance Fitting & Machining Technology', 'Year 1', 'Semester 2', '2025-03-23 13:44:51'),
(2739, 'THE121', 'Theology 1 (3 units)', 'Bachelor in Technology', 'Maintenance Fitting & Machining Technology', 'Year 1', 'Semester 1', '2025-03-23 13:44:51'),
(2740, 'THE122', 'Theology 2 (3 units)', 'Bachelor in Technology', 'Maintenance Fitting & Machining Technology', 'Year 1', 'Semester 2', '2025-03-23 13:44:51'),
(2741, 'MFM221', 'Machining Systems (3 units)', 'Bachelor in Technology', 'Maintenance Fitting & Machining Technology', 'Year 2', 'Semester 1', '2025-03-23 13:44:51'),
(2742, 'MFM222', 'Fitting Advanced Techniques (3 units)', 'Bachelor in Technology', 'Maintenance Fitting & Machining Technology', 'Year 2', 'Semester 2', '2025-03-23 13:44:51'),
(2743, 'PTR221', 'Professional Practice (3 units)', 'Bachelor in Technology', 'Maintenance Fitting & Machining Technology', 'Year 2', 'Semester 1', '2025-03-23 13:44:51'),
(2744, 'PTR222', 'Technical Writing (3 units)', 'Bachelor in Technology', 'Maintenance Fitting & Machining Technology', 'Year 2', 'Semester 2', '2025-03-23 13:44:51'),
(2745, 'THE221', 'Theology 3 (3 units)', 'Bachelor in Technology', 'Maintenance Fitting & Machining Technology', 'Year 2', 'Semester 1', '2025-03-23 13:44:51'),
(2746, 'THE222', 'Theology 4 (3 units)', 'Bachelor in Technology', 'Maintenance Fitting & Machining Technology', 'Year 2', 'Semester 2', '2025-03-23 13:44:51'),
(2747, 'MFM321', 'Machining Processes (3 units)', 'Bachelor in Technology', 'Maintenance Fitting & Machining Technology', 'Year 3', 'Semester 1', '2025-03-23 13:44:51'),
(2748, 'MFM322', 'Capstone Fitting Project (3 units)', 'Bachelor in Technology', 'Maintenance Fitting & Machining Technology', 'Year 3', 'Semester 2', '2025-03-23 13:44:51'),
(2749, 'PTR321', 'Industrial Relations (3 units)', 'Bachelor in Technology', 'Maintenance Fitting & Machining Technology', 'Year 3', 'Semester 1', '2025-03-23 13:44:51'),
(2750, 'PTR322', 'Project Management (3 units)', 'Bachelor in Technology', 'Maintenance Fitting & Machining Technology', 'Year 3', 'Semester 2', '2025-03-23 13:44:51'),
(2751, 'THE321', 'Theology 5 (3 units)', 'Bachelor in Technology', 'Maintenance Fitting & Machining Technology', 'Year 3', 'Semester 1', '2025-03-23 13:44:51'),
(2752, 'THE322', 'Theology 6 (3 units)', 'Bachelor in Technology', 'Maintenance Fitting & Machining Technology', 'Year 3', 'Semester 2', '2025-03-23 13:44:51'),
(2753, 'MFM421', 'Advanced Machining Techniques (3 units)', 'Bachelor in Technology', 'Maintenance Fitting & Machining Technology', 'Year 4', 'Semester 1', '2025-03-23 13:44:51'),
(2754, 'MFM422', 'Capstone Project (3 units)', 'Bachelor in Technology', 'Maintenance Fitting & Machining Technology', 'Year 4', 'Semester 2', '2025-03-23 13:44:51'),
(2755, 'PTR421', 'Supervision in Workplace (3 units)', 'Bachelor in Technology', 'Maintenance Fitting & Machining Technology', 'Year 4', 'Semester 1', '2025-03-23 13:44:51'),
(2756, 'PTR422', 'Project Study (3 units)', 'Bachelor in Technology', 'Maintenance Fitting & Machining Technology', 'Year 4', 'Semester 2', '2025-03-23 13:44:51'),
(2757, 'THE421', 'Theology 7 (3 units)', 'Bachelor in Technology', 'Maintenance Fitting & Machining Technology', 'Year 4', 'Semester 1', '2025-03-23 13:44:51'),
(2758, 'THE422', 'Theology 8 (3 units)', 'Bachelor in Technology', 'Maintenance Fitting & Machining Technology', 'Year 4', 'Semester 2', '2025-03-23 13:44:51'),
(2759, 'DINF121', 'Introduction to Programming (3 units)', 'Diploma In Technology', 'Information Technology', 'Year 1', 'Semester 1', '2025-03-23 13:44:51'),
(2760, 'DINF122', 'Computer Fundamentals (3 units)', 'Diploma In Technology', 'Information Technology', 'Year 1', 'Semester 2', '2025-03-23 13:44:51'),
(2761, 'PTR121', 'Basic Workshop Practice (3 units)', 'Diploma In Technology', 'Information Technology', 'Year 1', 'Semester 1', '2025-03-23 13:44:51'),
(2762, 'PTR122', 'Technical Drawing (3 units)', 'Diploma In Technology', 'Information Technology', 'Year 1', 'Semester 2', '2025-03-23 13:44:51'),
(2763, 'THE121', 'Theology 1 (3 units)', 'Diploma In Technology', 'Information Technology', 'Year 1', 'Semester 1', '2025-03-23 13:44:51'),
(2764, 'THE122', 'Theology 2 (3 units)', 'Diploma In Technology', 'Information Technology', 'Year 1', 'Semester 2', '2025-03-23 13:44:51'),
(2765, 'DINF221', 'Database Systems (3 units)', 'Diploma In Technology', 'Information Technology', 'Year 2', 'Semester 1', '2025-03-23 13:44:51'),
(2766, 'DINF222', 'Web Development (3 units)', 'Diploma In Technology', 'Information Technology', 'Year 2', 'Semester 2', '2025-03-23 13:44:51'),
(2767, 'PTR221', 'Professional Practice (3 units)', 'Diploma In Technology', 'Information Technology', 'Year 2', 'Semester 1', '2025-03-23 13:44:51'),
(2768, 'PTR222', 'Technical Writing (3 units)', 'Diploma In Technology', 'Information Technology', 'Year 2', 'Semester 2', '2025-03-23 13:44:51'),
(2769, 'THE221', 'Theology 3 (3 units)', 'Diploma In Technology', 'Information Technology', 'Year 2', 'Semester 1', '2025-03-23 13:44:51'),
(2770, 'THE222', 'Theology 4 (3 units)', 'Diploma In Technology', 'Information Technology', 'Year 2', 'Semester 2', '2025-03-23 13:44:51'),
(2771, 'DINF321', 'Software Engineering (3 units)', 'Diploma In Technology', 'Information Technology', 'Year 3', 'Semester 1', '2025-03-23 13:44:51'),
(2772, 'DINF322', 'Network Administration (3 units)', 'Diploma In Technology', 'Information Technology', 'Year 3', 'Semester 2', '2025-03-23 13:44:51'),
(2773, 'PTR321', 'Industrial Relations (3 units)', 'Diploma In Technology', 'Information Technology', 'Year 3', 'Semester 1', '2025-03-23 13:44:51'),
(2774, 'PTR322', 'Project Management (3 units)', 'Diploma In Technology', 'Information Technology', 'Year 3', 'Semester 2', '2025-03-23 13:44:51'),
(2775, 'THE321', 'Theology 5 (3 units)', 'Diploma In Technology', 'Information Technology', 'Year 3', 'Semester 1', '2025-03-23 13:44:51'),
(2776, 'THE322', 'Theology 6 (3 units)', 'Diploma In Technology', 'Information Technology', 'Year 3', 'Semester 2', '2025-03-23 13:44:51'),
(2777, 'DAUT121', 'Basic Automotive Systems (3 units)', 'Diploma In Technology', 'Automotive Technology', 'Year 1', 'Semester 1', '2025-03-23 13:44:51'),
(2778, 'DAUT122', 'Automotive Tools and Equipment (3 units)', 'Diploma In Technology', 'Automotive Technology', 'Year 1', 'Semester 2', '2025-03-23 13:44:51'),
(2779, 'PTR121', 'Basic Workshop Practice (3 units)', 'Diploma In Technology', 'Automotive Technology', 'Year 1', 'Semester 1', '2025-03-23 13:44:51'),
(2780, 'PTR122', 'Technical Drawing (3 units)', 'Diploma In Technology', 'Automotive Technology', 'Year 1', 'Semester 2', '2025-03-23 13:44:51'),
(2781, 'THE121', 'Theology 1 (3 units)', 'Diploma In Technology', 'Automotive Technology', 'Year 1', 'Semester 1', '2025-03-23 13:44:51'),
(2782, 'THE122', 'Theology 2 (3 units)', 'Diploma In Technology', 'Automotive Technology', 'Year 1', 'Semester 2', '2025-03-23 13:44:51'),
(2783, 'DAUT221', 'Engine Systems (3 units)', 'Diploma In Technology', 'Automotive Technology', 'Year 2', 'Semester 1', '2025-03-23 13:44:51'),
(2784, 'DAUT222', 'Electrical Systems (3 units)', 'Diploma In Technology', 'Automotive Technology', 'Year 2', 'Semester 2', '2025-03-23 13:44:51'),
(2785, 'PTR221', 'Professional Practice (3 units)', 'Diploma In Technology', 'Automotive Technology', 'Year 2', 'Semester 1', '2025-03-23 13:44:51'),
(2786, 'PTR222', 'Technical Writing (3 units)', 'Diploma In Technology', 'Automotive Technology', 'Year 2', 'Semester 2', '2025-03-23 13:44:51'),
(2787, 'THE221', 'Theology 3 (3 units)', 'Diploma In Technology', 'Automotive Technology', 'Year 2', 'Semester 1', '2025-03-23 13:44:51'),
(2788, 'THE222', 'Theology 4 (3 units)', 'Diploma In Technology', 'Automotive Technology', 'Year 2', 'Semester 2', '2025-03-23 13:44:51'),
(2789, 'DAUT321', 'Automotive Systems (3 units)', 'Diploma In Technology', 'Automotive Technology', 'Year 3', 'Semester 1', '2025-03-23 13:44:51'),
(2790, 'DAUT322', 'Vehicle Maintenance (3 units)', 'Diploma In Technology', 'Automotive Technology', 'Year 3', 'Semester 2', '2025-03-23 13:44:51'),
(2791, 'PTR321', 'Industrial Relations (3 units)', 'Diploma In Technology', 'Automotive Technology', 'Year 3', 'Semester 1', '2025-03-23 13:44:51'),
(2792, 'PTR322', 'Project Management (3 units)', 'Diploma In Technology', 'Automotive Technology', 'Year 3', 'Semester 2', '2025-03-23 13:44:51'),
(2793, 'THE321', 'Theology 5 (3 units)', 'Diploma In Technology', 'Automotive Technology', 'Year 3', 'Semester 1', '2025-03-23 13:44:51'),
(2794, 'THE322', 'Theology 6 (3 units)', 'Diploma In Technology', 'Automotive Technology', 'Year 3', 'Semester 2', '2025-03-23 13:44:51'),
(2795, 'DIN121', 'Basics of Instrumentation (3 units)', 'Diploma In Technology', 'Instrumentation Technology', 'Year 1', 'Semester 1', '2025-03-23 13:44:51'),
(2796, 'DIN122', 'Instrumentation Tools and Practices (3 units)', 'Diploma In Technology', 'Instrumentation Technology', 'Year 1', 'Semester 2', '2025-03-23 13:44:51'),
(2797, 'PTR121', 'Basic Workshop Practice (3 units)', 'Diploma In Technology', 'Instrumentation Technology', 'Year 1', 'Semester 1', '2025-03-23 13:44:51'),
(2798, 'PTR122', 'Technical Drawing (3 units)', 'Diploma In Technology', 'Instrumentation Technology', 'Year 1', 'Semester 2', '2025-03-23 13:44:51'),
(2799, 'THE121', 'Theology 1 (3 units)', 'Diploma In Technology', 'Instrumentation Technology', 'Year 1', 'Semester 1', '2025-03-23 13:44:51'),
(2800, 'THE122', 'Theology 2 (3 units)', 'Diploma In Technology', 'Instrumentation Technology', 'Year 1', 'Semester 2', '2025-03-23 13:44:51'),
(2801, 'DIN221', 'Introduction to Measurement (3 units)', 'Diploma In Technology', 'Instrumentation Technology', 'Year 2', 'Semester 1', '2025-03-23 13:44:51'),
(2802, 'DIN222', 'Instrumentation Devices (3 units)', 'Diploma In Technology', 'Instrumentation Technology', 'Year 2', 'Semester 2', '2025-03-23 13:44:51'),
(2803, 'PTR221', 'Professional Practice (3 units)', 'Diploma In Technology', 'Instrumentation Technology', 'Year 2', 'Semester 1', '2025-03-23 13:44:51'),
(2804, 'PTR222', 'Technical Writing (3 units)', 'Diploma In Technology', 'Instrumentation Technology', 'Year 2', 'Semester 2', '2025-03-23 13:44:51'),
(2805, 'THE221', 'Theology 3 (3 units)', 'Diploma In Technology', 'Instrumentation Technology', 'Year 2', 'Semester 1', '2025-03-23 13:44:51'),
(2806, 'THE222', 'Theology 4 (3 units)', 'Diploma In Technology', 'Instrumentation Technology', 'Year 2', 'Semester 2', '2025-03-23 13:44:51'),
(2807, 'DIN321', 'Instrumentation Systems (3 units)', 'Diploma In Technology', 'Instrumentation Technology', 'Year 3', 'Semester 1', '2025-03-23 13:44:51'),
(2808, 'DIN322', 'Control Systems (3 units)', 'Diploma In Technology', 'Instrumentation Technology', 'Year 3', 'Semester 2', '2025-03-23 13:44:51'),
(2809, 'PTR321', 'Industrial Relations (3 units)', 'Diploma In Technology', 'Instrumentation Technology', 'Year 3', 'Semester 1', '2025-03-23 13:44:51'),
(2810, 'PTR322', 'Project Management (3 units)', 'Diploma In Technology', 'Instrumentation Technology', 'Year 3', 'Semester 2', '2025-03-23 13:44:51'),
(2811, 'THE321', 'Theology 5 (3 units)', 'Diploma In Technology', 'Instrumentation Technology', 'Year 3', 'Semester 1', '2025-03-23 13:44:51'),
(2812, 'THE322', 'Theology 6 (3 units)', 'Diploma In Technology', 'Instrumentation Technology', 'Year 3', 'Semester 2', '2025-03-23 13:44:51'),
(2813, 'DELE121', 'Electronic Circuits (3 units)', 'Diploma In Technology', 'Electronics Technology', 'Year 1', 'Semester 1', '2025-03-23 13:44:51'),
(2814, 'DELE122', 'Digital Electronics (3 units)', 'Diploma In Technology', 'Electronics Technology', 'Year 1', 'Semester 2', '2025-03-23 13:44:51'),
(2815, 'PTR121', 'Basic Workshop Practice (3 units)', 'Diploma In Technology', 'Electronics Technology', 'Year 1', 'Semester 1', '2025-03-23 13:44:51'),
(2816, 'PTR122', 'Technical Drawing (3 units)', 'Diploma In Technology', 'Electronics Technology', 'Year 1', 'Semester 2', '2025-03-23 13:44:51'),
(2817, 'THE121', 'Theology 1 (3 units)', 'Diploma In Technology', 'Electronics Technology', 'Year 1', 'Semester 1', '2025-03-23 13:44:51'),
(2818, 'THE122', 'Theology 2 (3 units)', 'Diploma In Technology', 'Electronics Technology', 'Year 1', 'Semester 2', '2025-03-23 13:44:51'),
(2819, 'DELE221', 'Microcontroller Programming (3 units)', 'Diploma In Technology', 'Electronics Technology', 'Year 2', 'Semester 1', '2025-03-23 13:44:51'),
(2820, 'DELE222', 'Analog Electronics (3 units)', 'Diploma In Technology', 'Electronics Technology', 'Year 2', 'Semester 2', '2025-03-23 13:44:51'),
(2821, 'PTR221', 'Professional Practice (3 units)', 'Diploma In Technology', 'Electronics Technology', 'Year 2', 'Semester 1', '2025-03-23 13:44:51'),
(2822, 'PTR222', 'Technical Writing (3 units)', 'Diploma In Technology', 'Electronics Technology', 'Year 2', 'Semester 2', '2025-03-23 13:44:51'),
(2823, 'THE221', 'Theology 3 (3 units)', 'Diploma In Technology', 'Electronics Technology', 'Year 2', 'Semester 1', '2025-03-23 13:44:51'),
(2824, 'THE222', 'Theology 4 (3 units)', 'Diploma In Technology', 'Electronics Technology', 'Year 2', 'Semester 2', '2025-03-23 13:44:51'),
(2825, 'DELE321', 'Advanced Electronics (3 units)', 'Diploma In Technology', 'Electronics Technology', 'Year 3', 'Semester 1', '2025-03-23 13:44:51'),
(2826, 'DELE322', 'Communication Systems (3 units)', 'Diploma In Technology', 'Electronics Technology', 'Year 3', 'Semester 2', '2025-03-23 13:44:51'),
(2827, 'PTR321', 'Industrial Relations (3 units)', 'Diploma In Technology', 'Electronics Technology', 'Year 3', 'Semester 1', '2025-03-23 13:44:51'),
(2828, 'PTR322', 'Project Management (3 units)', 'Diploma In Technology', 'Electronics Technology', 'Year 3', 'Semester 2', '2025-03-23 13:44:51'),
(2829, 'THE321', 'Theology 5 (3 units)', 'Diploma In Technology', 'Electronics Technology', 'Year 3', 'Semester 1', '2025-03-23 13:44:51'),
(2830, 'THE322', 'Theology 6 (3 units)', 'Diploma In Technology', 'Electronics Technology', 'Year 3', 'Semester 2', '2025-03-23 13:44:51'),
(2831, 'DETL121', 'Electrical Basics (3 units)', 'Diploma In Technology', 'Electrical Technology', 'Year 1', 'Semester 1', '2025-03-23 13:44:51'),
(2832, 'DETL122', 'Circuit Analysis (3 units)', 'Diploma In Technology', 'Electrical Technology', 'Year 1', 'Semester 2', '2025-03-23 13:44:51'),
(2833, 'PTR121', 'Basic Workshop Practice (3 units)', 'Diploma In Technology', 'Electrical Technology', 'Year 1', 'Semester 1', '2025-03-23 13:44:51'),
(2834, 'PTR122', 'Technical Drawing (3 units)', 'Diploma In Technology', 'Electrical Technology', 'Year 1', 'Semester 2', '2025-03-23 13:44:51'),
(2835, 'THE121', 'Theology 1 (3 units)', 'Diploma In Technology', 'Electrical Technology', 'Year 1', 'Semester 1', '2025-03-23 13:44:51'),
(2836, 'THE122', 'Theology 2 (3 units)', 'Diploma In Technology', 'Electrical Technology', 'Year 1', 'Semester 2', '2025-03-23 13:44:51'),
(2837, 'DETL221', 'Power Systems (3 units)', 'Diploma In Technology', 'Electrical Technology', 'Year 2', 'Semester 1', '2025-03-23 13:44:51'),
(2838, 'DETL222', 'Control Systems (3 units)', 'Diploma In Technology', 'Electrical Technology', 'Year 2', 'Semester 2', '2025-03-23 13:44:51'),
(2839, 'PTR221', 'Professional Practice (3 units)', 'Diploma In Technology', 'Electrical Technology', 'Year 2', 'Semester 1', '2025-03-23 13:44:51'),
(2840, 'PTR222', 'Technical Writing (3 units)', 'Diploma In Technology', 'Electrical Technology', 'Year 2', 'Semester 2', '2025-03-23 13:44:51'),
(2841, 'THE221', 'Theology 3 (3 units)', 'Diploma In Technology', 'Electrical Technology', 'Year 2', 'Semester 1', '2025-03-23 13:44:51'),
(2842, 'THE222', 'Theology 4 (3 units)', 'Diploma In Technology', 'Electrical Technology', 'Year 2', 'Semester 2', '2025-03-23 13:44:51'),
(2843, 'DETL321', 'Power Distribution (3 units)', 'Diploma In Technology', 'Electrical Technology', 'Year 3', 'Semester 1', '2025-03-23 13:44:51'),
(2844, 'DETL322', 'Automation and Control (3 units)', 'Diploma In Technology', 'Electrical Technology', 'Year 3', 'Semester 2', '2025-03-23 13:44:51'),
(2845, 'PTR321', 'Industrial Relations (3 units)', 'Diploma In Technology', 'Electrical Technology', 'Year 3', 'Semester 1', '2025-03-23 13:44:51'),
(2846, 'PTR322', 'Project Management (3 units)', 'Diploma In Technology', 'Electrical Technology', 'Year 3', 'Semester 2', '2025-03-23 13:44:51'),
(2847, 'THE321', 'Theology 5 (3 units)', 'Diploma In Technology', 'Electrical Technology', 'Year 3', 'Semester 1', '2025-03-23 13:44:51'),
(2848, 'THE322', 'Theology 6 (3 units)', 'Diploma In Technology', 'Electrical Technology', 'Year 3', 'Semester 2', '2025-03-23 13:44:51'),
(2851, 'INF111', 'Introduction To Information Technology', 'Bachelor in Education', 'Information Technology', '1', 'Semester 1', '2025-03-28 17:38:59'),
(2857, 'MFW311', 'Introduction to MFW333', 'Diploma In Technology', 'Metal Welding & Fabrication Technology', '3', 'Semester 1', '2025-03-28 18:57:35'),
(2858, 'TEMP002', 'Technology Created', 'Bachelor in Education', 'Metal Welding & Fabrication Technology', 'Year 1', 'Semester 1', '2025-03-28 19:29:56'),
(2859, 'TEMP002', 'Technology Created', 'Bachelor in Education', 'Electronics Technology', 'Year 1', 'Semester 1', '2025-03-30 21:33:43'),
(2860, 'TEMP003', 'Year Level Created', 'Bachelor in Education', 'Electronics Technology', 'Year 3', 'Semester 1', '2025-03-30 21:34:01'),
(2861, 'TEMP003', 'Year Level Created', 'Bachelor in Education', 'Electronics Technology', 'Year 3', 'Semester 1', '2025-03-30 21:52:29'),
(2862, 'TEMP003', 'Year Level Created', 'Bachelor in Education', 'Electronics Technology', 'Year 3', 'Semester 1', '2025-03-30 21:57:21'),
(2863, 'PH31', 'Year Level Created', 'Bachelor in Education', 'Electronics Technology', 'Year 3', 'Semester 1', '2025-03-30 22:03:02'),
(2864, 'PH32', 'Year Level Created', 'Bachelor in Education', 'Electronics Technology', 'Year 3', 'Semester 2', '2025-03-30 22:03:02'),
(2865, 'BED 311', 'Introduction To Teaching', 'Bachelor in Education', 'Electronics Technology', 'Year 3', 'Semester 1', '2025-03-30 22:03:38'),
(2866, 'INF411', 'Introduction to Networking', 'Bachelor in Technology', 'Electrical Technology', 'Year 2', 'Semester 1', '2025-03-31 23:37:35');

-- --------------------------------------------------------

--
-- Table structure for table `course_registrations`
--

CREATE TABLE `course_registrations` (
  `id` int(11) NOT NULL,
  `registration_id` int(11) NOT NULL,
  `course_id` varchar(20) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `course_registrations`
--

INSERT INTO `course_registrations` (`id`, `registration_id`, `course_id`) VALUES
(1, 30, 'BED 311'),
(2, 30, 'PH31'),
(3, 30, 'TEMP003'),
(4, 30, 'TEMP003'),
(5, 30, 'TEMP003'),
(6, 30, 'PH32'),
(7, 31, 'BED 311'),
(8, 31, 'PH31'),
(9, 31, 'TEMP003'),
(10, 31, 'TEMP003'),
(11, 31, 'TEMP003'),
(12, 31, 'PH32'),
(13, 24, 'INF321'),
(14, 25, 'INF321'),
(15, 26, 'INF321'),
(16, 27, 'INF321'),
(17, 28, 'INF321'),
(18, 29, 'INF321'),
(19, 32, 'INF321'),
(20, 24, 'PTR321'),
(21, 25, 'PTR321'),
(22, 26, 'PTR321'),
(23, 27, 'PTR321'),
(24, 28, 'PTR321'),
(25, 29, 'PTR321'),
(26, 32, 'PTR321'),
(27, 24, 'THE321'),
(28, 25, 'THE321'),
(29, 26, 'THE321'),
(30, 27, 'THE321'),
(31, 28, 'THE321'),
(32, 29, 'THE321'),
(33, 32, 'THE321'),
(34, 19, 'INF421'),
(35, 20, 'INF421'),
(36, 34, 'INF421'),
(37, 35, 'INF421'),
(38, 19, 'PTR421'),
(39, 20, 'PTR421'),
(40, 34, 'PTR421'),
(41, 35, 'PTR421'),
(42, 19, 'THE421'),
(43, 20, 'THE421'),
(44, 34, 'THE421'),
(45, 35, 'THE421'),
(46, 21, 'TEMP002'),
(47, 37, 'TEMP002'),
(48, 30, 'TEMP003'),
(49, 31, 'TEMP003'),
(50, 33, 'TEMP003'),
(51, 30, 'TEMP003'),
(52, 31, 'TEMP003'),
(53, 33, 'TEMP003'),
(54, 30, 'TEMP003'),
(55, 31, 'TEMP003'),
(56, 33, 'TEMP003'),
(57, 30, 'PH31'),
(58, 31, 'PH31'),
(59, 33, 'PH31'),
(60, 30, 'BED 311'),
(61, 31, 'BED 311'),
(62, 33, 'BED 311'),
(76, 38, 'ELE421'),
(77, 38, 'PTR421'),
(78, 38, 'THE421'),
(79, 38, 'ELE422'),
(80, 38, 'PTR422'),
(81, 38, 'THE422'),
(82, 39, 'MFW311'),
(83, 40, 'BED 311'),
(84, 40, 'PH31'),
(85, 40, 'TEMP003'),
(86, 40, 'TEMP003'),
(87, 40, 'TEMP003'),
(88, 40, 'PH32'),
(89, 41, 'INF421'),
(90, 41, 'PTR421'),
(91, 41, 'THE421'),
(92, 41, 'INF422'),
(93, 41, 'PTR422'),
(94, 41, 'THE422'),
(95, 42, 'INF421'),
(96, 42, 'PTR421'),
(97, 42, 'THE421'),
(98, 42, 'INF422'),
(99, 42, 'PTR422'),
(100, 42, 'THE422'),
(101, 43, 'INF321'),
(102, 43, 'PTR321'),
(103, 43, 'THE321'),
(104, 43, 'INF322'),
(105, 43, 'PTR322'),
(106, 43, 'THE322');

-- --------------------------------------------------------

--
-- Table structure for table `payments`
--

CREATE TABLE `payments` (
  `payment_id` int(11) NOT NULL,
  `student_id` varchar(50) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `payment_date` datetime NOT NULL,
  `payment_method` enum('cash','bank_transfer','credit_card') NOT NULL,
  `transaction_id` varchar(100) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `payments`
--

INSERT INTO `payments` (`payment_id`, `student_id`, `amount`, `payment_date`, `payment_method`, `transaction_id`) VALUES
(39, '222222', 4600.00, '2024-10-25 23:08:51', 'credit_card', 'pi_3QDwcfKnyip3i5w11iGe9JsR'),
(41, '222006', 4465.00, '2024-10-26 02:43:22', 'credit_card', 'pi_3QDzyGKnyip3i5w11D7cXPCD'),
(42, '222007', 4385.00, '2024-10-26 02:48:08', 'credit_card', 'pi_3QE02sKnyip3i5w11QIRk5zk'),
(43, '222008', 4485.00, '2024-10-26 02:50:33', 'credit_card', 'pi_3QE05DKnyip3i5w11pU1AyK5'),
(44, '2222009', 4365.00, '2024-10-26 03:27:11', 'credit_card', 'pi_3QE0efKnyip3i5w10CsOgy7f'),
(45, '222222', 4465.00, '2024-10-26 04:29:52', 'credit_card', 'pi_3QE1dKKnyip3i5w11z3grf7C'),
(46, '211079', 4235.00, '2024-10-26 05:41:31', 'credit_card', 'pi_3QE2kUKnyip3i5w10IQ0Ekfh'),
(47, '223028', 4365.00, '2024-10-26 13:29:01', 'credit_card', 'pi_3QEA35Knyip3i5w10kVnqCQp'),
(48, '222006', 4385.00, '2024-10-27 00:40:14', 'credit_card', 'pi_3QEKWeKnyip3i5w10dquBMbv'),
(49, '211078', 4385.00, '2024-10-27 06:13:13', 'credit_card', 'pi_3QEPitKnyip3i5w10zzI7mQB'),
(50, '211000', 4385.00, '2024-10-27 11:43:13', 'credit_card', 'pi_3QEUsFKnyip3i5w10QwxCIo1'),
(51, '000000', 4465.00, '2024-10-27 15:49:52', 'credit_card', 'pi_3QEYiwKnyip3i5w11nH6lobq'),
(52, '211051', 4465.00, '2024-10-27 22:48:33', 'credit_card', 'pi_3QEfG7Knyip3i5w11zItDH9O'),
(53, '222115', 4465.00, '2024-10-28 00:37:51', 'credit_card', 'pi_3QEgxtKnyip3i5w10Fv1fvld'),
(54, '222118', 4465.00, '2024-10-28 04:38:27', 'credit_card', 'pi_3QEkijKnyip3i5w11tyxl45H'),
(56, '221046', 4535.00, '2024-10-31 04:11:13', 'credit_card', 'pi_3QFpj1Knyip3i5w11zfch80u'),
(57, '221087', 4235.00, '2024-10-31 20:06:47', 'credit_card', 'pi_3QG4dmKnyip3i5w10yNr5CUT'),
(58, '222007', 4465.00, '2024-11-01 07:16:13', 'credit_card', 'pi_3QGF5bKnyip3i5w10w7wSLSX'),
(60, '202020', 4465.00, '2024-11-13 04:29:17', 'credit_card', 'pi_3QKYCeKnyip3i5w10IBexNs3'),
(64, '201042', 4465.00, '2024-11-30 05:10:51', 'credit_card', 'pi_3QQixBKnyip3i5w11TZiyh3S'),
(65, '201042', 4600.00, '2025-01-06 20:19:03', 'credit_card', 'pi_3QeMlNKnyip3i5w100ibKNfT'),
(66, '221046', 4600.00, '2025-02-19 22:11:13', 'credit_card', 'pi_3QuLU3Knyip3i5w111SVVjDO'),
(67, '201043', 4600.00, '2025-03-28 23:51:16', 'credit_card', 'pi_3R7mgAKnyip3i5w10RX9kiHb'),
(68, '211001', 4600.00, '2025-03-30 20:02:33', 'credit_card', 'pi_3R8S3vKnyip3i5w10DlB3Czh'),
(69, '201044', 4600.00, '2025-03-30 20:09:14', 'credit_card', 'pi_3R8SAOKnyip3i5w115ZtsB6d'),
(70, 'tyson', 4235.00, '2025-03-30 21:05:21', 'credit_card', 'pi_3R8T2iKnyip3i5w10HeqgTA9'),
(71, 'mayah', 4485.00, '2025-03-30 21:35:14', 'credit_card', 'pi_3R8TVcKnyip3i5w10x9rkjp9'),
(72, 'jemillah', 4465.00, '2025-03-31 09:34:49', 'credit_card', 'pi_3R8ejyKnyip3i5w10YZrQA6E'),
(73, 'rynold', 4600.00, '2025-03-31 09:45:55', 'credit_card', 'pi_3R8eukKnyip3i5w114pXRSdv'),
(74, 'eli', 4515.00, '2025-03-31 09:52:55', 'credit_card', 'pi_3R8f1WKnyip3i5w10fFcYnaK'),
(75, '211042', 4385.00, '2025-03-31 18:50:03', 'credit_card', 'pi_3R8nPJKnyip3i5w11b1FHXco'),
(76, '221042', 4385.00, '2025-03-31 19:33:26', 'credit_card', 'pi_3R8o5JKnyip3i5w114eMHO8A'),
(77, '231042', 4185.00, '2025-03-31 20:04:43', 'credit_card', 'pi_3R8oZZKnyip3i5w11gValaRh'),
(78, '241042', 4600.00, '2025-03-31 20:39:29', 'credit_card', 'pi_3R8p7DKnyip3i5w10N8LploE'),
(79, '251042', 4600.00, '2025-03-31 23:01:28', 'credit_card', 'pi_3R8rKcKnyip3i5w10RSwzlkw'),
(80, '2525', 4385.00, '2025-03-31 23:28:54', 'credit_card', 'pi_3R8rlAKnyip3i5w10uOJax0q');

-- --------------------------------------------------------

--
-- Table structure for table `registrations`
--

CREATE TABLE `registrations` (
  `registration_id` int(11) NOT NULL,
  `student_id` varchar(50) NOT NULL,
  `registration_date` datetime NOT NULL,
  `course_id` varchar(50) NOT NULL,
  `semester` varchar(20) NOT NULL,
  `academic_year` varchar(9) NOT NULL,
  `status` enum('current','previous') NOT NULL DEFAULT 'current'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `registrations`
--

INSERT INTO `registrations` (`registration_id`, `student_id`, `registration_date`, `course_id`, `semester`, `academic_year`, `status`) VALUES
(19, '201043', '2025-03-29 20:18:01', '', 'Semester 1', '2025', ''),
(20, '201042', '2025-03-29 20:48:14', '', 'Semester 1', '2025', ''),
(21, '221046', '2025-03-30 18:37:25', '', 'Semester 1', '2025', ''),
(22, '211001', '2025-03-30 20:04:09', '', 'Semester 1', '2025', ''),
(23, '201044', '2025-03-30 20:11:04', '', 'Semester 1', '2025', ''),
(24, 'tyson', '2025-03-30 21:06:41', '2603', 'Semester 1', '2025', ''),
(25, 'tyson', '2025-03-30 21:06:41', '2605', 'Semester 1', '2025', ''),
(26, 'tyson', '2025-03-30 21:06:41', '2607', 'Semester 1', '2025', ''),
(27, 'tyson', '2025-03-30 21:06:41', '2604', 'Semester 1', '2025', ''),
(28, 'tyson', '2025-03-30 21:06:41', '2606', 'Semester 1', '2025', ''),
(29, 'tyson', '2025-03-30 21:06:41', '2608', 'Semester 1', '2025', ''),
(30, 'mayah', '2025-03-31 08:55:58', '', 'Semester 1', '2025', ''),
(31, 'jemillah', '2025-03-31 09:35:45', '', 'Semester 1', '2025', ''),
(32, 'tyson', '2025-03-30 21:06:41', '', 'Semester 1', '2025', ''),
(33, 'mayah', '2025-03-31 08:55:58', '', 'Semester 1', '2025', ''),
(34, '201042', '2024-11-30 05:12:26', '', 'Semester 1', '2025', ''),
(35, '201043', '2025-03-29 20:18:01', '', 'Semester 1', '2025', ''),
(36, '201044', '2025-03-30 20:11:04', '', 'Semester 1', '2025', ''),
(37, '221046', '2025-03-30 18:37:25', '', 'Semester 1', '2025', ''),
(38, 'rynold', '2025-03-31 09:48:15', '', 'Semester 1', '2025', ''),
(39, 'eli', '2025-03-31 09:54:37', '', 'Semester 1', '2025', ''),
(40, '221046', '2025-03-31 18:38:25', '', 'Semester 1', '2025', 'current'),
(41, '201042', '2025-03-31 18:41:03', '', 'Semester 1', '2025', 'current'),
(42, '211001', '2025-03-31 18:43:03', '', 'Semester 1', '2025', 'current'),
(43, '211042', '2025-03-31 18:52:00', '', 'Semester 1', '2025', 'current');

-- --------------------------------------------------------

--
-- Table structure for table `students`
--

CREATE TABLE `students` (
  `student_id` varchar(50) NOT NULL,
  `first_name` varchar(50) NOT NULL,
  `last_name` varchar(50) NOT NULL,
  `gender` enum('Male','Female','Other') NOT NULL,
  `phone_number` varchar(15) NOT NULL,
  `residential_address` varchar(255) NOT NULL,
  `home_province` varchar(50) NOT NULL,
  `guardian_name` varchar(100) NOT NULL,
  `guardian_occupation` varchar(100) NOT NULL,
  `guardian_phone_number` varchar(15) NOT NULL,
  `guardian_email` varchar(100) NOT NULL,
  `student_email` varchar(100) NOT NULL,
  `dob` date NOT NULL,
  `program` varchar(50) NOT NULL,
  `tech` varchar(50) NOT NULL,
  `payment_status` enum('paid','not_paid') DEFAULT 'not_paid',
  `registration_status` enum('unregistered','pending','registered') DEFAULT 'unregistered',
  `year_level` varchar(20) DEFAULT 'Temporary',
  `registration_timestamp` timestamp NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `students`
--

INSERT INTO `students` (`student_id`, `first_name`, `last_name`, `gender`, `phone_number`, `residential_address`, `home_province`, `guardian_name`, `guardian_occupation`, `guardian_phone_number`, `guardian_email`, `student_email`, `dob`, `program`, `tech`, `payment_status`, `registration_status`, `year_level`, `registration_timestamp`) VALUES
('000000', 'Boss', 'Mahn', 'Female', '00000000', 'Morata', 'Manus Province', 'Monna', 'Singer', '00000000', '<EMAIL>', '<EMAIL>', '2001-02-02', 'Bachelor in Technology', 'Electrical Technology', 'paid', 'unregistered', 'Year 4', '2024-10-27 15:51:30'),
('201042', 'Emmanuel', 'Kokele', 'Male', '71662383', 'Nikints Street, Morata 2, Port Moresby', 'National Capital District', 'John', 'Teacher ', '71662383', '<EMAIL>', '<EMAIL>', '2001-11-30', 'Bachelor in Technology', 'Information Technology', 'paid', 'registered', 'Year 4', '2025-03-31 18:41:03'),
('201043', 'Simon', 'Komae', 'Male', '000-0000', 'DBTI Campus', 'National Capital District', 'Not Provided', 'Not Provided', '000-0000', '<EMAIL>', '<EMAIL>', '2000-01-01', 'Bachelor in Technology', 'Information Technology', 'paid', 'registered', 'Year 4', '2025-03-31 18:57:43'),
('201044', 'Branden', 'Keliso', 'Male', '000-0000', 'DBTI Campus', 'National Capital District', 'Not Provided', 'Not Provided', '000-0000', '<EMAIL>', '<EMAIL>', '2000-01-01', '', '', 'paid', 'unregistered', 'Temporary', '2025-03-30 20:08:09'),
('202020', 'Regina', 'Poggy', 'Male', '71662383', 'Nikints Street, Morata 2, Port Moresby', 'National Capital District', 'John', 'Teacher ', '7373', '<EMAIL>', '<EMAIL>', '2024-04-10', 'Bachelor in Technology', 'Information Technology', 'paid', 'unregistered', 'Year 4', '2024-11-13 04:30:31'),
('20202222', 'reggy', 'poggy', 'Male', '000-0000', 'DBTI Campus', 'National Capital District', 'Not Provided', 'Not Provided', '000-0000', '<EMAIL>', '<EMAIL>', '2000-01-01', '', '', 'not_paid', 'unregistered', 'Temporary', '2024-11-12 21:02:32'),
('211000', 'Waninara', 'Niba', 'Male', '72021718', 'DBTI', 'East New Britain Province', 'Mike', 'IT', '72021719', '<EMAIL>', '<EMAIL>', '1999-05-13', 'Bachelor in Technology', 'Information Technology', 'paid', 'registered', 'Year 4', '2025-03-31 19:31:35'),
('211001', 'Edward', 'Abraham', 'Male', '000-0000', 'DBTI Campus', 'National Capital District', 'Not Provided', 'Not Provided', '000-0000', '<EMAIL>', '<EMAIL>', '2000-01-01', 'Bachelor in Technology', 'Information Technology', 'paid', 'registered', 'Year 4', '2025-03-31 18:43:03'),
('211011', 'Edward', 'Abraham', 'Male', '000-0000', 'DBTI Campus', 'National Capital District', 'Not Provided', 'Not Provided', '000-0000', '<EMAIL>', '<EMAIL>', '2000-01-01', '', '', 'not_paid', 'unregistered', 'Temporary', '2025-03-06 06:43:53'),
('211015', 'Roy', 'Baki', 'Male', '000-0000', 'DBTI Campus', 'National Capital District', 'Not Provided', 'Not Provided', '000-0000', '<EMAIL>', '<EMAIL>', '2000-01-01', '', '', 'not_paid', 'unregistered', 'Temporary', '2024-10-26 23:48:33'),
('211042', 'John', 'Smith', 'Male', '000-0000', 'DBTI Campus', 'National Capital District', 'Not Provided', 'Not Provided', '000-0000', '<EMAIL>', '<EMAIL>', '2000-01-01', 'Bachelor in Technology', 'Information Technology', 'paid', 'registered', 'Year 3', '2025-03-31 18:52:00'),
('211051', 'Wayne', 'Kasi', 'Male', '000-0000', 'DBTI Campus', 'National Capital District', 'Not Provided', 'Not Provided', '000-0000', '<EMAIL>', '<EMAIL>', '2000-01-01', '', '', 'paid', 'unregistered', 'Temporary', '2024-10-27 22:46:18'),
('211078', 'Adam', 'Reynolds', 'Male', '79188390', 'DBTi', 'National Capital District', 'Murive', 'Eve', '75745453', '<EMAIL>', '<EMAIL>', '1999-08-09', 'Bachelor in Technology', 'Automotive Technology', 'paid', 'unregistered', 'Year 4', '2024-10-27 06:15:34'),
('211079', 'John', 'Bosco', 'Male', '79188390', 'dsr', 'Jiwaka Province', 'murivee', 'singer', '7544321', '<EMAIL>', '<EMAIL>', '2024-10-26', 'Bachelor in Technology', 'Automotive Technology', 'paid', 'unregistered', 'Year 4', '2024-10-26 05:43:20'),
('221042', 'Joe', 'Blow', 'Male', '716 623832', 'Nikints Street, Morata 2, Port Moresby', 'National Capital District', 'John', 'Driver', '716 62383', '<EMAIL>', '<EMAIL>', '2009-04-01', 'Bachelor in Technology', 'Instrumentation Technology', 'paid', 'registered', 'Year 3', '2025-03-31 19:41:28'),
('221046', 'Simpson', 'Karo', 'Male', '71662383', 'Nikints Street, Morata 2, Port Moresby', 'National Capital District', 'T', 'Y', '71662383', '<EMAIL>', '<EMAIL>', '2024-08-06', 'Bachelor in Education', 'Electronics Technology', 'paid', 'registered', 'Year 3', '2025-03-31 18:38:25'),
('221087', 'Shirley', 'Nganu', 'Female', '79188390', 'MHC', 'Morobe Province', 'Sr. Carmencita', 'Salesian Educator', '7544321', '<EMAIL>', '<EMAIL>', '2024-11-02', 'Bachelor in Technology', 'Information Technology', 'paid', 'unregistered', 'Year 3', '2024-10-31 20:08:53'),
('222006', 'Joe', 'Blow', 'Male', '74586554', 'DBTI', 'Chimbu Province', 'James', 'Teacher', '71458632', '<EMAIL>', '<EMAIL>', '2024-10-26', 'Diploma In Technology', 'Information Technology', 'paid', 'unregistered', 'Year 2', '2024-10-26 02:45:22'),
('222007', 'Enos ', 'Nemo', 'Male', '6362', 'Bdjd', 'National Capital District', 'Hdhe', 'Hehe', 'Heheh', '<EMAIL>', '<EMAIL>', '2023-11-07', 'Bachelor in Technology', 'Electrical Technology', 'paid', 'unregistered', 'Year 4', '2024-11-01 07:20:23'),
('222008', 'Ezra ', 'Sani', 'Male', '71662383', 'Morata', 'Autonomous Region of Bougainville', 'Malts', 'hj', '79538675', '<EMAIL>', '<EMAIL>', '2000-02-02', 'Bachelor in Technology', 'Automotive Technology', 'paid', 'unregistered', 'Year 3', '2024-10-26 03:20:40'),
('222115', 'Remona', 'Lapan', 'Female', '74534523', 'DBTI', 'West New Britain Province', 'Pauline Morro', 'Teacher', '71458269', '<EMAIL>', '<EMAIL>', '2025-01-28', 'Bachelor in Technology', 'Information Technology', 'paid', 'unregistered', 'Year 4', '2024-10-28 00:41:07'),
('222118', 'Samson', 'Stanley', 'Male', '74534523', 'DBTI', 'East New Britain Province', 'Pauline Morro', 'Teacher', '71458269', '<EMAIL>', '<EMAIL>', '1999-12-12', 'Bachelor in Technology', 'Information Technology', 'paid', 'unregistered', 'Year 4', '2024-10-28 04:40:25'),
('22215', 'Remona', 'Lapan', 'Male', '000-0000', 'DBTI Campus', 'National Capital District', 'Not Provided', 'Not Provided', '000-0000', '<EMAIL>', '<EMAIL>', '2000-01-01', '', '', 'not_paid', 'unregistered', 'Temporary', '2024-10-28 00:32:18'),
('2222009', 'James', 'Bond', 'Male', '71552635', 'n', 'Jiwaka Province', 'n', 'n', 'n', '<EMAIL>', '<EMAIL>', '2000-02-02', 'Diploma In Technology', 'Automotive Technology', 'paid', 'unregistered', 'Year 3', '2024-10-26 03:28:34'),
('222222', '222222', '222222', 'Male', '79188390', 'dsr', 'Jiwaka Province', 'murivee', 'singer', '7544321', '<EMAIL>', '<EMAIL>', '2009-02-02', 'Bachelor in Technology', 'Automotive Technology', 'paid', 'unregistered', 'Year 4', '2024-10-26 04:31:32'),
('223028', 'Finkelstine', 'Milekorofi', 'Male', '71244458', 'dbti', 'New Ireland Province', 'wanix', 'teacher', '74125896', '<EMAIL>', '<EMAIL>', '2024-10-26', 'Diploma In Technology', 'Electronics Technology', 'paid', 'unregistered', 'Year 3', '2024-10-26 13:30:48'),
('231042', 'Jack', 'Sparrow', 'Male', '716 62383', 'Nikints Street, Morata 2, Port Moresby', 'National Capital District', 'Sparrow', 'Ship Caption', '716 62383', '<EMAIL>', '<EMAIL>', '2000-04-01', 'Bachelor in Technology', 'Information Technology', 'paid', 'registered', 'Year 2', '2025-03-31 20:05:43'),
('233369', 'joe ', 'ben', 'Male', '000-0000', 'DBTI Campus', 'National Capital District', 'Not Provided', 'Not Provided', '000-0000', '<EMAIL>', '<EMAIL>', '2000-01-01', '', '', 'not_paid', 'unregistered', 'Temporary', '2024-10-27 05:53:35'),
('241042', 'Stanley', 'Michael', 'Male', '716 62383', 'Nikints Street, Morata 2, Port Moresby', 'National Capital District', 'Michael', 'Carpenter', '716 62383', '<EMAIL>', '<EMAIL>', '1999-04-01', 'Bachelor in Technology', 'Maintenance Fitting & Machining Technology', 'paid', 'registered', 'Year 4', '2025-03-31 20:40:28'),
('251042', 'Rynold', 'Ryan', 'Male', '716 62383', 'Nikints Street, Morata 2, Port Moresby', 'National Capital District', 'Abraham ', 'Teacher ', '716 62383', '<EMAIL>', '<EMAIL>', '2019-04-01', 'Bachelor in Education', 'Electronics Technology', 'paid', 'registered', 'Year 3', '2025-03-31 23:03:37'),
('2525', 'Noel', 'Kibai', 'Female', '713 6564954', 'Nikints Street, Morata 2, Port Moresby', 'National Capital District', 'Kibai', 'Teacher', '716 62383', '<EMAIL>', '<EMAIL>', '1988-04-01', 'Bachelor in Technology', 'Information Technology', 'paid', 'registered', 'Year 4', '2025-03-31 23:32:06'),
('cashier', 'Marie', 'Ovia', 'Male', '000-0000', 'DBTI Campus', 'National Capital District', 'Not Provided', 'Not Provided', '000-0000', '<EMAIL>', '<EMAIL>', '2000-01-01', '', '', 'not_paid', 'unregistered', 'Temporary', '2024-10-25 23:10:35'),
('cashier2', 'Grace', 'Oliver', 'Male', '000-0000', 'DBTI Campus', 'National Capital District', 'Not Provided', 'Not Provided', '000-0000', '<EMAIL>', '<EMAIL>', '2000-01-01', '', '', 'not_paid', 'unregistered', 'Temporary', '2024-10-28 02:43:51'),
('ekokele', 'Emmanuel', 'Kokele', 'Male', '000-0000', 'DBTI Campus', 'National Capital District', 'Not Provided', 'Not Provided', '000-0000', '<EMAIL>', '<EMAIL>', '2000-01-01', '', '', 'not_paid', 'unregistered', 'Temporary', '2025-03-29 19:53:44'),
('eli', 'eli', 'telek', 'Male', '000-0000', 'DBTI Campus', 'National Capital District', 'Not Provided', 'Not Provided', '000-0000', '<EMAIL>', '<EMAIL>', '2000-01-01', 'Diploma In Technology', 'Metal Welding & Fabrication Technology', 'paid', 'unregistered', '3', '2025-03-31 09:51:32'),
('jemillah', 'Jemillah', 'Lepo', 'Male', '000-0000', 'DBTI Campus', 'National Capital District', 'Not Provided', 'Not Provided', '000-0000', '<EMAIL>', '<EMAIL>', '2000-01-01', 'Bachelor in Education', 'Electronics Technology', 'paid', 'unregistered', 'Year 3', '2025-03-31 09:33:22'),
('mayah', 'Mayah', 'Lepo', 'Male', '000-0000', 'DBTI Campus', 'National Capital District', 'Not Provided', 'Not Provided', '000-0000', '<EMAIL>', '<EMAIL>', '2000-01-01', 'Bachelor in Education', 'Electronics Technology', 'paid', 'unregistered', 'Year 3', '2025-03-30 21:32:20'),
('registrar', 'Leonen', 'Coreea', 'Male', '000-0000', 'DBTI Campus', 'National Capital District', 'Not Provided', 'Not Provided', '000-0000', '<EMAIL>', '<EMAIL>', '2000-01-01', '', '', 'not_paid', 'unregistered', 'Temporary', '2024-10-25 23:11:10'),
('registrar2', 'Mary', 'Jane', 'Male', '000-0000', 'DBTI Campus', 'National Capital District', 'Not Provided', 'Not Provided', '000-0000', '<EMAIL>', '<EMAIL>', '2000-01-01', '', '', 'not_paid', 'unregistered', 'Temporary', '2024-10-28 02:49:49'),
('registrar3', 'Lenn', 'Correa', 'Male', '', '', '', '', '', '', '', '', '0000-00-00', '', '', 'not_paid', 'unregistered', 'Temporary', '2025-03-31 19:15:04'),
('rynold', 'rynold', 'kokele', 'Male', '000-0000', 'DBTI Campus', 'National Capital District', 'Not Provided', 'Not Provided', '000-0000', '<EMAIL>', '<EMAIL>', '2000-01-01', 'Bachelor in Technology', 'Electronics Technology', 'paid', 'unregistered', 'Year 4', '2025-03-31 09:44:42'),
('tyson', 'Tyson', 'Tope', 'Male', '000-0000', 'DBTI Campus', 'National Capital District', 'Not Provided', 'Not Provided', '000-0000', '<EMAIL>', '<EMAIL>', '2000-01-01', 'Bachelor in Technology', 'Information Technology', 'paid', 'unregistered', 'Year 3', '2025-03-30 21:02:16');

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `user_id` int(11) NOT NULL,
  `username` varchar(50) NOT NULL,
  `student_id` varchar(50) DEFAULT NULL,
  `password` varchar(255) NOT NULL,
  `role` enum('student','cashier','registrar','admin') NOT NULL,
  `last_login` datetime DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`user_id`, `username`, `student_id`, `password`, `role`, `last_login`, `created_at`, `updated_at`) VALUES
(1, 'ADMIN', NULL, '$2y$10$owO5RL3gNMopqn235Z/hku03MI2MZEtiJiHUSrHgAqmfLPpGDyO4W', 'admin', NULL, '2024-10-08 00:08:32', '2024-10-28 01:49:41'),
(36, '222007', '222007', '$2y$10$pvjflBf4Wgfwm7fEVpZngOxNunoNSWZRGbrVMwKncDFH5KOXdCDhy', 'student', NULL, '2024-10-26 02:46:12', '2024-11-01 07:14:06'),
(37, '222008', '222008', '$2y$10$5z.9ooYgTveN.BvBH1No7e7qKpeKNc0auMO75eRWENJOILtTgohiG', 'student', NULL, '2024-10-26 02:49:29', '2024-10-26 02:49:29'),
(40, '211079', '211079', '$2y$10$y5OVlQ3CHA/AVnQxE9gOFu632wKaNbeyeh2iwe9c9o2IgthBAuFUy', 'student', NULL, '2024-10-26 05:32:36', '2024-10-26 05:32:36'),
(41, '223028', '223028', '$2y$10$.MGp6OnNKKWwh8XbFPct8ehw/PcFdTkW4MIYg5FfQJcPII.9F0SLW', 'student', NULL, '2024-10-26 13:24:34', '2024-10-26 13:36:42'),
(42, '211015', '211015', '$2y$10$v7Q2MNmL857CX6X5ch39dOssq5.3HcBaIrIOlYb7dtn2n3SYFnBtS', 'student', NULL, '2024-10-26 23:48:33', '2024-10-26 23:48:33'),
(47, '211078', '211078', '$2y$10$70MjyqmLdaSjWQsOAH01seHQKbgHjCOc9tN4N.Z7nW.a1CJMfoPDy', 'student', NULL, '2024-10-27 06:09:59', '2024-10-27 06:09:59'),
(48, '211000', '211000', '$2y$10$R89Je6Jt9.qnt7B/Wer0LeFIi8Og96iWvYmdCpUqVIYRRpRNFC/Jq', 'student', NULL, '2024-10-27 11:42:11', '2025-03-31 19:00:27'),
(50, '211051', '211051', '$2y$10$CP3bWfYQIDvBVtKyc2Hg9OSwozvQBut60jdmpXqy6k0wiS/vq4cTy', 'student', NULL, '2024-10-27 22:46:18', '2024-10-27 22:58:05'),
(52, '222115', '222115', '$2y$10$vRl9eAG1cbCMCJDES4GjF.aue3KvJH9LB606m3sGyg7Zmag7Pxqc6', 'student', NULL, '2024-10-28 00:34:33', '2024-10-28 00:34:33'),
(54, 'cashier2', 'cashier2', '$2y$10$GWK9lX88x1XByMKUoC/e9OQGCFfrOSlAec3VLtCFCJeXsZiauFoDi', 'cashier', NULL, '2024-10-28 02:43:51', '2024-10-28 02:50:40'),
(56, '222118', '222118', '$2y$10$QGH4Ew6Q9bq2qOa6Gs4GRuRvY/jtv6o.ftyYVMzv2TYasg6XPraN6', 'student', NULL, '2024-10-28 04:35:07', '2024-10-28 04:35:07'),
(57, '221087', '221087', '$2y$10$Adiuy/mAEnYv5/WFWmkQ.ee0ImZcX1.68FUplaar/SPGkg790DVYO', 'student', NULL, '2024-10-29 11:36:49', '2024-10-29 11:36:49'),
(58, '221046', '221046', '$2y$10$iGLny2uH7T2HsGwoqJ5oCuiqACpfzwSlUtLMDCV8pHH73CmwmnRJq', 'student', NULL, '2024-10-31 04:09:25', '2024-10-31 04:09:25'),
(64, 'registrar2', 'registrar2', '$2y$10$dfj8CO8yYR4.ZdS1CKBAtuinkb5Oai2BGjVem.auW/zQV1RcGcHUW', 'registrar', NULL, '2024-11-29 19:56:26', '2025-03-31 19:13:24'),
(65, '201042', '201042', '$2y$10$jq2W9eA.97L1swsm2qaIJu9AB2kTaHzq3.9HP17pzEmLkOPgYP8Ze', 'student', NULL, '2024-11-29 21:29:00', '2024-11-29 21:29:00'),
(67, '211001', '211001', '$2y$10$fjFtlerYbgfvaOuXqmMC1OxwNrVrIcuVZpRPtdevLIAGecC6hdRIC', 'student', NULL, '2025-03-06 06:45:47', '2025-03-06 06:45:47'),
(68, '201043', '201043', '$2y$10$tsTBlthTWymoxbQMRtX3TuFrZGwJDJ.vUxvHdhAteUQ4r03/AMjnW', 'student', NULL, '2025-03-28 23:50:10', '2025-03-28 23:50:10'),
(69, 'ekokele', 'ekokele', '$2y$10$MdvyGzS/w3/07B.IDdl3Bu.seV5yPqWOsLG2AUwYsj5sJ9JsmeoPO', 'registrar', NULL, '2025-03-29 19:53:44', '2025-03-29 19:53:44'),
(70, '201044', '201044', '$2y$10$FYluHGuDaScx3EZUwntqP.OS4TiN55WJOpFX9JnLTujMoLMyZCuVC', 'student', NULL, '2025-03-30 20:08:09', '2025-03-30 20:08:09'),
(71, 'tyson', 'tyson', '$2y$10$7Yfp.PwFbiDkoU0v/XnJ1OnG4.1wJ/0NRd4IHS.MigQ1BBqsHxaZy', 'student', NULL, '2025-03-30 21:02:16', '2025-03-30 21:02:16'),
(72, 'mayah', 'mayah', '$2y$10$pfQGKlppJk5J/7.DkyvSmu9.G.wil3ItVkyLFmoxktMftkhJnWg9q', 'student', NULL, '2025-03-30 21:32:20', '2025-03-30 21:32:20'),
(73, 'jemillah', 'jemillah', '$2y$10$ICy2X8jERe2Ne/cDzeMbKuCuqZz8lJMaj.oVEeoh045WgNN7WkDyu', 'student', NULL, '2025-03-31 09:33:22', '2025-03-31 09:33:22'),
(74, 'rynold', 'rynold', '$2y$10$ulgMW0gcbaWLDl3Qq/pGQOVk0KWLTTgqZGiitHW98Y5j5pgqBJRNy', 'student', NULL, '2025-03-31 09:44:42', '2025-03-31 09:44:42'),
(75, 'eli', 'eli', '$2y$10$P6lwJp2YhhpvLGDwyDDmquzkjRF1R98G7xZ/oE5Jy9dHhu4yha2bW', 'student', NULL, '2025-03-31 09:51:32', '2025-03-31 09:51:32'),
(76, '211042', '211042', '$2y$10$SbN55q4wJtamklSdEJTFJeFcOl0.REzORJeRqlzsFsuq4wP/064aa', 'student', NULL, '2025-03-31 18:48:56', '2025-03-31 18:48:56'),
(77, 'registrar3', 'registrar3', '$2y$10$ep5JEQtvnww9WCD7W7j8L.tywjd5HDTPyWaHKZlMCar752fBrRf76', 'registrar', NULL, '2025-03-31 19:15:04', '2025-03-31 19:15:04'),
(78, '221042', '221042', '$2y$10$vXucRiA22oY9oX8k/KohwOkE5oZmXrMppCut95RsF1PjcDA/xeZQS', 'student', NULL, '2025-03-31 19:32:30', '2025-03-31 19:32:30'),
(79, '231042', '231042', '$2y$10$fBvzG5JDVBPkoWWJffhG2uUK./Pw5Q.rz1y3lQGhwGyJn.nwrYiem', 'student', NULL, '2025-03-31 20:03:49', '2025-03-31 20:03:49'),
(80, '241042', '241042', '$2y$10$Nh57ITNKsxL8ojANT3kdjeM4WD43bAbENt/LVNvrrdtZ.4L0zOwae', 'student', NULL, '2025-03-31 20:37:45', '2025-03-31 20:37:45'),
(81, '251042', '251042', '$2y$10$wwQaAY3I.qFR0r.PHqEfoeJMnQIqzPeHvr/77/pwnfm.1IxHzHP9e', 'student', NULL, '2025-03-31 22:59:35', '2025-03-31 22:59:35'),
(82, '2525', '2525', '$2y$10$fIxe5SieALoqzA5Gd0xqguwZaNuMAuk5WY2FBXh/ch5i7YZkNrfYW', 'student', NULL, '2025-03-31 23:24:04', '2025-03-31 23:24:04');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `courses`
--
ALTER TABLE `courses`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `course_registrations`
--
ALTER TABLE `course_registrations`
  ADD PRIMARY KEY (`id`),
  ADD KEY `registration_id` (`registration_id`);

--
-- Indexes for table `payments`
--
ALTER TABLE `payments`
  ADD PRIMARY KEY (`payment_id`),
  ADD KEY `student_id` (`student_id`),
  ADD KEY `idx_payment_date` (`payment_date`);

--
-- Indexes for table `registrations`
--
ALTER TABLE `registrations`
  ADD PRIMARY KEY (`registration_id`),
  ADD KEY `student_id` (`student_id`),
  ADD KEY `idx_registration_date` (`registration_date`);

--
-- Indexes for table `students`
--
ALTER TABLE `students`
  ADD PRIMARY KEY (`student_id`),
  ADD KEY `idx_student_email` (`student_email`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`user_id`),
  ADD UNIQUE KEY `username` (`username`),
  ADD KEY `fk_user_student` (`student_id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `courses`
--
ALTER TABLE `courses`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2867;

--
-- AUTO_INCREMENT for table `course_registrations`
--
ALTER TABLE `course_registrations`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=107;

--
-- AUTO_INCREMENT for table `payments`
--
ALTER TABLE `payments`
  MODIFY `payment_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=81;

--
-- AUTO_INCREMENT for table `registrations`
--
ALTER TABLE `registrations`
  MODIFY `registration_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=44;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `user_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=83;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `course_registrations`
--
ALTER TABLE `course_registrations`
  ADD CONSTRAINT `course_registrations_ibfk_1` FOREIGN KEY (`registration_id`) REFERENCES `registrations` (`registration_id`);

--
-- Constraints for table `payments`
--
ALTER TABLE `payments`
  ADD CONSTRAINT `payments_ibfk_1` FOREIGN KEY (`student_id`) REFERENCES `students` (`student_id`);

--
-- Constraints for table `registrations`
--
ALTER TABLE `registrations`
  ADD CONSTRAINT `registrations_ibfk_1` FOREIGN KEY (`student_id`) REFERENCES `students` (`student_id`);

--
-- Constraints for table `users`
--
ALTER TABLE `users`
  ADD CONSTRAINT `fk_user_student` FOREIGN KEY (`student_id`) REFERENCES `students` (`student_id`) ON DELETE SET NULL ON UPDATE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
