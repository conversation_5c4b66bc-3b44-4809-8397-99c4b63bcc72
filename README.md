<div align="center" id="top">
  <img src="img/logo.webp" alt="DBTI Online Registration" />

  &#xa0;

  <!-- <a href="https://dbtionline.waghitech.com">Demo</a> -->
</div>

<h1 align="center">DBTI Online Registration System</h1>

<p align="center">
  <img alt="Github top language" src="https://img.shields.io/badge/top%20language-PHP-blue">
  <img alt="Github language count" src="https://img.shields.io/badge/languages-3-blue">
  <img alt="License" src="https://img.shields.io/badge/license-MIT-blue">
</p>

<p align="center">
  <a href="#dart-about">About</a> &#xa0; | &#xa0;
  <a href="#sparkles-features">Features</a> &#xa0; | &#xa0;
  <a href="#rocket-technologies">Technologies</a> &#xa0; | &#xa0;
  <a href="#security-improvements">Security Improvements</a> &#xa0; | &#xa0;
  <a href="#white_check_mark-requirements">Requirements</a> &#xa0; | &#xa0;
  <a href="#checkered_flag-starting">Starting</a> &#xa0; | &#xa0;
  <a href="#memo-license">License</a>
</p>

<br>

## :dart: About ##

A comprehensive web-based registration and student management system for Don Bosco Technological Institute. This system allows students to register for courses, make payments, and manage their academic information online.

## :sparkles: Features ##

:heavy_check_mark: Student Registration and Course Selection\
:heavy_check_mark: Online Payment Processing with Stripe\
:heavy_check_mark: PDF Document Generation\
:heavy_check_mark: Email Notifications\
:heavy_check_mark: Role-based Access Control\
:heavy_check_mark: Student Data Management

## :rocket: Technologies ##

The following technologies were used in this project:

- [PHP](https://www.php.net/)
- [MySQL](https://www.mysql.com/)
- [HTML/CSS/JavaScript](https://developer.mozilla.org/en-US/docs/Web)
- [PHPMailer](https://github.com/PHPMailer/PHPMailer)
- [FPDF](http://www.fpdf.org/)
- [Stripe API](https://stripe.com/docs/api)

## :shield: Security Improvements ##

The following security improvements have been implemented:

### 1. Configuration Management
- Created a centralized `config.php` file to store all configuration settings
- Removed hardcoded credentials from PHP files
- Added support for environment variables through a `.env` file
- Created `.env.example` as a template for environment variables
- Added `.gitignore` to prevent sensitive files from being committed

### 2. CSRF Protection
- Implemented CSRF token generation and validation
- Added CSRF tokens to all forms
- Added validation of CSRF tokens in form processing scripts

### 3. Secure Session Management
- Added session security settings
- Implemented session expiration
- Added session regeneration on login to prevent session fixation
- Fixed session initialization order to prevent warnings

### 4. Secure File Handling
- Implemented secure random filename generation
- Added file type validation
- Created dedicated directories for uploads and temporary files

### 5. Input Validation and Sanitization
- Added functions for input sanitization
- Implemented email and phone number validation
- Added password strength validation

### 6. Improved Error Handling
- Enhanced error messages
- Added security logging
- Prevented exposure of sensitive information in error messages

### 7. Secure Authentication
- Improved password handling
- Added protection against brute force attacks
- Enhanced login form security

### 8. Secure Payment Processing
- Removed hardcoded Stripe API keys
- Added configuration for Stripe integration

### 9. Code Organization
- Reorganized codebase into proper folder structure following PHP best practices
- Separated public files from application logic
- Organized files by functionality (admin, api, controllers, etc.)
- Updated all file paths and includes to work with new structure
- Improved maintainability and security through proper separation of concerns

## :white_check_mark: Requirements ##

Before starting :checkered_flag:, you need to have:

- [PHP](https://www.php.net/) 7.4 or higher
- [MySQL](https://www.mysql.com/) 5.7 or higher
- [Composer](https://getcomposer.org/) for PHP dependency management
- Web server (Apache/Nginx)

## :file_folder: Project Structure ##

The project has been reorganized following PHP best practices:

```
dbtionline/
├── public/                     # Web-accessible files (document root)
│   ├── index.php              # Main entry point
│   ├── about.php              # Public pages
│   ├── login.php
│   ├── registration.php
│   ├── assets/                # Static assets
│   │   ├── css/               # Stylesheets
│   │   ├── js/                # JavaScript files
│   │   └── img/               # Images
│   └── *.html                 # SEO verification files
├── src/                       # Application source code
│   ├── Controllers/           # Page controllers and business logic
│   ├── Models/               # Database models (future use)
│   ├── Services/             # Business services (future use)
│   └── Helpers/              # Utility functions (future use)
├── admin/                     # Admin interface
│   ├── admin_dashboard.php
│   ├── manage_courses.php
│   └── assets/               # Admin-specific assets
├── api/                       # API endpoints
│   ├── get_*.php             # Data retrieval endpoints
│   ├── ajax_*.php            # AJAX handlers
│   └── stripe/               # Payment processing
├── config/                    # Configuration files
│   ├── config.php            # Main configuration
│   ├── db_conn.php           # Database connection
│   └── security_helpers.php  # Security functions
├── database/                  # Database files and migrations
│   ├── dbtionline.sqlite     # SQLite database
│   └── migrations/           # SQL migration files
├── storage/                   # Non-web-accessible storage
│   ├── logs/                 # Application logs
│   ├── cache/                # Cache files
│   ├── temp/                 # Temporary files
│   └── backups/              # Database backups
├── vendor/                    # Composer dependencies
├── scripts/                   # Utility scripts
├── docs/                      # Documentation
├── bootstrap.php              # Application bootstrap
└── composer.json              # Dependency management
```

## :checkered_flag: Starting ##

```bash
# Clone this project
$ git clone https://github.com/yourusername/dbtionline

# Access
$ cd dbtionline

# Copy environment file
$ cp .env.example .env

# Edit .env file with your credentials
$ nano .env

# Install dependencies
$ composer install

# Set up web server to point to public/ directory
# For development, you can use PHP's built-in server:
$ php -S localhost:8000 -t public/
```

## :bulb: Environment Variables ##

The following environment variables can be set in the `.env` file:

```
# Database Configuration
DB_HOST=localhost
DB_USER=database_username
DB_PASSWORD=database_password
DB_NAME=database_name

# Email Configuration
SMTP_HOST=smtp.example.com
SMTP_PORT=465
SMTP_SECURE=ssl
SMTP_AUTH=true
REGISTRAR_EMAIL=<EMAIL>
REGISTRAR_PASSWORD=email_password
CASHIER_EMAIL=<EMAIL>
CASHIER_PASSWORD=email_password

# Application Settings
APP_URL=https://example.com

# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_your_secret_key
STRIPE_PUBLISHABLE_KEY=pk_test_your_publishable_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret
```

## :memo: License ##

This project is under license from MIT. For more details, see the [LICENSE](LICENSE.md) file.

&#xa0;

<a href="#top">Back to top</a>
