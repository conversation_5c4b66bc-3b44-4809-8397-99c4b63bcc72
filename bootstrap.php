<?php
/**
 * Bootstrap file for DBTI Online Registration System
 * This file sets up the application environment and includes necessary files
 */

// Define the root directory
define('ROOT_DIR', __DIR__);

// Define secure access constant
if (!defined('SECURE_ACCESS')) {
    define('SECURE_ACCESS', true);
}

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Include configuration
require_once ROOT_DIR . '/config/config.php';

// Include database connection
require_once ROOT_DIR . '/config/db_conn.php';

// Include Composer autoloader if it exists
if (file_exists(ROOT_DIR . '/vendor/autoload.php')) {
    require_once ROOT_DIR . '/vendor/autoload.php';
}

/**
 * Helper function to get the correct path for includes
 * @param string $path The relative path from root
 * @return string The absolute path
 */
function get_path($path) {
    return ROOT_DIR . '/' . ltrim($path, '/');
}

/**
 * Helper function to get the correct URL for assets
 * @param string $asset The asset path
 * @return string The correct URL
 */
function asset($asset) {
    // Determine if we're in a subdirectory
    $script_dir = dirname($_SERVER['SCRIPT_NAME']);
    $base_url = $script_dir === '/' ? '' : $script_dir;
    
    return $base_url . '/' . ltrim($asset, '/');
}

/**
 * Helper function to get the correct URL for pages
 * @param string $page The page path
 * @return string The correct URL
 */
function url($page) {
    // Determine if we're in a subdirectory
    $script_dir = dirname($_SERVER['SCRIPT_NAME']);
    $base_url = $script_dir === '/' ? '' : $script_dir;
    
    return $base_url . '/' . ltrim($page, '/');
}
