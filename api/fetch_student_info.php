<?php
session_start();
require_once '../config/db_conn.php';

if (!isset($_SESSION['username']) || $_SESSION['role'] !== 'student') {
    header("Location: login.php");
    exit();
}

$student_id = $_SESSION['username'];
$sql = "SELECT * FROM students WHERE student_id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("s", $student_id);
$stmt->execute();
$result = $stmt->get_result();
$student_info = $result->fetch_assoc();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Student Information</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="student-info-container">
        <h2>Student Information</h2>
        <div class="info-section">
            <h3>Personal Details</h3>
            <p><strong>Student ID:</strong> <?php echo htmlspecialchars($student_info['student_id']); ?></p>
            <p><strong>Name:</strong> <?php echo htmlspecialchars($student_info['first_name'] . ' ' . $student_info['last_name']); ?></p>
            <p><strong>Gender:</strong> <?php echo htmlspecialchars($student_info['gender']); ?></p>
            <p><strong>Date of Birth:</strong> <?php echo htmlspecialchars($student_info['dob']); ?></p>
            <p><strong>Email:</strong> <?php echo htmlspecialchars($student_info['student_email']); ?></p>
            <p><strong>Phone Number:</strong> <?php echo htmlspecialchars($student_info['phone_number']); ?></p>
            <p><strong>Address:</strong> <?php echo html
