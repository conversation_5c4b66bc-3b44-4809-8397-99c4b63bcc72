<?php
require_once '../config/db_conn.php';

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Log all requests for debugging
error_log("get_technologies.php called with program: " . ($_GET['program'] ?? 'not provided'));

$program = $_GET['program'] ?? '';

if (empty($program)) {
    header('Content-Type: application/json');
    echo json_encode([]);
    error_log("get_technologies.php - No program provided");
    exit;
}

// Use DISTINCT to ensure no duplicates and exclude placeholders
$sql = "SELECT DISTINCT technology FROM courses
        WHERE program = ?
        AND course_id NOT LIKE 'PLACEHOLDER%'
        AND course_id NOT LIKE 'TEMP%'
        ORDER BY technology";

// Prepare the statement
try {
    $stmt = $conn->prepare($sql);

    if (!$stmt) {
        throw new Exception("Prepare failed: " . $conn->error);
    }

    $stmt->bind_param("s", $program);

    if (!$stmt->execute()) {
        throw new Exception("Execute failed: " . $stmt->error);
    }

    $result = $stmt->get_result();

    $technologies = [];
    while ($row = $result->fetch_assoc()) {
        // Only add if not already in the array (extra protection against duplicates)
        if (!in_array($row['technology'], $technologies)) {
            $technologies[] = $row['technology'];
        }
    }

    // Log the number of technologies found
    error_log("get_technologies.php - Found " . count($technologies) . " technologies for program: $program");
    error_log("Technologies: " . implode(", ", $technologies));

    // If no technologies found, try a fallback query without excluding placeholders
    if (empty($technologies)) {
        $fallback_sql = "SELECT DISTINCT technology FROM courses WHERE program = ? ORDER BY technology";
        $fallback_stmt = $conn->prepare($fallback_sql);

        if ($fallback_stmt) {
            $fallback_stmt->bind_param("s", $program);
            $fallback_stmt->execute();
            $fallback_result = $fallback_stmt->get_result();

            while ($row = $fallback_result->fetch_assoc()) {
                if (!in_array($row['technology'], $technologies)) {
                    $technologies[] = $row['technology'];
                }
            }

            error_log("get_technologies.php - Fallback query found " . count($technologies) . " technologies");
        }
    }

    // Add a count of matching courses with this program
    $count_sql = "SELECT COUNT(*) as count FROM courses WHERE program = ?";
    $count_stmt = $conn->prepare($count_sql);
    if ($count_stmt) {
        $count_stmt->bind_param("s", $program);
        $count_stmt->execute();
        $count_result = $count_stmt->get_result();
        $count_data = $count_result->fetch_assoc();
        error_log("Total courses for program $program: " . $count_data['count']);
    }

    // Return the technologies as a JSON array
    header('Content-Type: application/json');
    echo json_encode($technologies);
} catch (Exception $e) {
    error_log("Error in get_technologies.php: " . $e->getMessage());
    header('Content-Type: application/json');
    echo json_encode(["error" => $e->getMessage()]);
}
?>
