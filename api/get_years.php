<?php
require_once '../config/db_conn.php';

// Enable error logging for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

$program = $_GET['program'] ?? '';
// Support both 'tech' and 'technology' parameter names
$tech = $_GET['technology'] ?? $_GET['tech'] ?? '';

// Log received parameters for debugging
error_log("get_years.php - Received parameters: program=$program, tech=$tech");

if (empty($program) || empty($tech)) {
    echo json_encode([]);
    error_log("get_years.php - Missing parameters");
    exit;
}

// Use DISTINCT to ensure no duplicates and exclude placeholder records
$sql = "SELECT DISTINCT year_level FROM courses
        WHERE program = ?
        AND technology = ?
        AND course_id NOT LIKE 'TEMP%'
        AND course_id NOT LIKE 'PH%'
        ORDER BY year_level";

// Fallback query to include placeholder records if no results found
$fallback_sql = "SELECT DISTINCT year_level FROM courses
                 WHERE program = ?
                 AND technology = ?
                 ORDER BY year_level";

$stmt = $conn->prepare($sql);
$stmt->bind_param("ss", $program, $tech);
$stmt->execute();
$result = $stmt->get_result();

$years = [];
while ($row = $result->fetch_assoc()) {
    // Only add if not already in the array (extra protection against duplicates)
    if (!in_array($row['year_level'], $years)) {
        $years[] = $row['year_level'];
    }
}

// If no years found, try the fallback query including placeholder records
if (empty($years)) {
    error_log("get_years.php - No years found with first query, trying fallback query");
    $fallback_stmt = $conn->prepare($fallback_sql);
    $fallback_stmt->bind_param("ss", $program, $tech);
    $fallback_stmt->execute();
    $fallback_result = $fallback_stmt->get_result();

    while ($row = $fallback_result->fetch_assoc()) {
        if (!in_array($row['year_level'], $years)) {
            $years[] = $row['year_level'];
        }
    }
}

// Count total records for these parameters for debugging
$count_sql = "SELECT COUNT(*) as total FROM courses WHERE program = ? AND technology = ?";
$count_stmt = $conn->prepare($count_sql);
$count_stmt->bind_param("ss", $program, $tech);
$count_stmt->execute();
$count_result = $count_stmt->get_result();
$count_data = $count_result->fetch_assoc();
$total_count = $count_data['total'];

error_log("get_years.php - Found " . count($years) . " unique years out of $total_count total records");

// Return the year levels as a JSON array
header('Content-Type: application/json');
echo json_encode($years);
?>
