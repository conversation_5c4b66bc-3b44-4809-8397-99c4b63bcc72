<?php
// Start session before including any files that might try to modify session settings
session_start();

// Define secure access constant for config.php
define('SECURE_ACCESS', true);

// Include configuration and database connection
require_once '../config/config.php';
require_once '../config/db_conn.php';

$courses_sql = "SELECT c.course_name, r.semester, r.academic_year
                FROM registrations r
                JOIN courses c ON r.course_id = c.course_id
                WHERE r.student_id = ?";
$courses_stmt = $conn->prepare($courses_sql);
$courses_stmt->bind_param("s", $_SESSION['username']);
$courses_stmt->execute();
$courses_result = $courses_stmt->get_result();

$courses = [];
while ($course = $courses_result->fetch_assoc()) {
    $courses[] = $course;
}

$courses_stmt->close();
$conn->close();

header('Content-Type: application/json');
echo json_encode($courses);
