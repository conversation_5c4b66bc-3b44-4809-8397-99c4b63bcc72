-- Database Backup Before Reset
-- Generated: 2025-05-22 01:55:47
-- SQLite Database File: /home/<USER>/Documents/git/dbtionline/database/dbtionline.sqlite

-- SQLite version: 3.40.1

-- Note: This is a SQLite database backup
-- To restore, execute these SQL statements in sequence

PRAGMA foreign_keys = OFF;
BEGIN TRANSACTION;

-- Table structure for table `courses`
DROP TABLE IF EXISTS `courses`;
CREATE TABLE `courses` (
  `id` INTEGER PRIMARY KEY AUTOINCREMENT,
  `course_id` TEXT NOT NULL,
  `course_name` TEXT NOT NULL,
  `program` TEXT NOT NULL,
  `technology` TEXT NOT NULL,
  `year_level` TEXT NOT NULL,
  `semester` TEXT NOT NULL DEFAULT 'Semester 1',
  `created_at` TIMESTAMP DEFAULT 'CURRENT_TIMESTAMP'
);


-- No data for table `courses`

-- Table structure for table `students`
DROP TABLE IF EXISTS `students`;
CREATE TABLE `students` (
  `student_id` TEXT PRIMARY KEY,
  `first_name` TEXT NOT NULL,
  `last_name` TEXT NOT NULL,
  `gender` TEXT NOT NULL,
  `dob` DATE NOT NULL,
  `phone_number` TEXT NOT NULL,
  `student_email` TEXT NOT NULL,
  `residential_address` TEXT NOT NULL,
  `home_province` TEXT NOT NULL,
  `guardian_name` TEXT NOT NULL,
  `guardian_occupation` TEXT NOT NULL,
  `guardian_phone_number` TEXT NOT NULL,
  `guardian_email` TEXT NOT NULL,
  `program` TEXT,
  `tech` TEXT,
  `year_level` TEXT DEFAULT 'Temporary',
  `payment_status` TEXT DEFAULT 'not_paid',
  `registration_status` TEXT DEFAULT 'unregistered',
  `registration_timestamp` TIMESTAMP DEFAULT 'CURRENT_TIMESTAMP'
);


-- No data for table `students`

-- Table structure for table `users`
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
  `user_id` INTEGER PRIMARY KEY AUTOINCREMENT,
  `username` TEXT NOT NULL,
  `student_id` TEXT,
  `password` TEXT NOT NULL,
  `role` TEXT NOT NULL,
  `last_login` DATETIME,
  `created_at` TIMESTAMP DEFAULT 'CURRENT_TIMESTAMP',
  `updated_at` TIMESTAMP DEFAULT 'CURRENT_TIMESTAMP'
);


-- No data for table `users`

-- Table structure for table `registrations`
DROP TABLE IF EXISTS `registrations`;
CREATE TABLE `registrations` (
  `registration_id` INTEGER PRIMARY KEY AUTOINCREMENT,
  `student_id` TEXT NOT NULL,
  `registration_date` DATETIME NOT NULL,
  `semester` TEXT NOT NULL,
  `academic_year` TEXT NOT NULL,
  `status` TEXT NOT NULL DEFAULT 'current'
);


-- No data for table `registrations`

-- Table structure for table `course_registrations`
DROP TABLE IF EXISTS `course_registrations`;
CREATE TABLE `course_registrations` (
  `id` INTEGER PRIMARY KEY AUTOINCREMENT,
  `registration_id` INTEGER NOT NULL,
  `course_id` TEXT NOT NULL
);


-- No data for table `course_registrations`

-- Table structure for table `payments`
DROP TABLE IF EXISTS `payments`;
CREATE TABLE `payments` (
  `payment_id` INTEGER PRIMARY KEY AUTOINCREMENT,
  `student_id` TEXT NOT NULL,
  `amount` REAL NOT NULL,
  `payment_date` DATETIME NOT NULL,
  `payment_method` TEXT NOT NULL,
  `transaction_id` TEXT,
  `payment_status` TEXT DEFAULT 'completed',
  `created_at` TIMESTAMP DEFAULT 'CURRENT_TIMESTAMP'
);


-- No data for table `payments`

COMMIT;
PRAGMA foreign_keys = ON;
