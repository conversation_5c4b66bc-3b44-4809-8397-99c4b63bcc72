<?php
session_start();
require_once '../config/db_conn.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_year'])) {
    $student_id = $_POST['student_id'];
    $new_year = $_POST['new_year'];
    
    $updateSql = "UPDATE students SET year_level = ? WHERE student_id = ?";
    $stmt = $conn->prepare($updateSql);
    $stmt->bind_param("ss", $new_year, $student_id);
    
    if ($stmt->execute()) {
        $_SESSION['success'] = "Year level updated successfully!";
    } else {
        $_SESSION['error'] = "Failed to update year level.";
    }
    
    header("Location: admin_settings.php");
    exit();
}

$sql = "SELECT * FROM students ORDER BY student_id";
$result = $conn->query($sql);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Manage Student Year Levels</title>
    <link rel="stylesheet" href="../public/assets/css/styles.css">
    <style>
        .current-year {
            font-weight: bold;
            padding: 5px 10px;
            border-radius: 4px;
            display: inline-block;
        }
        .success-message {
            background-color: #4CAF50;
            color: white;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .error-message {
            background-color: #f44336;
            color: white;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        select {
            padding: 8px;
            margin: 3px;
            border-radius: 4px;
            border: 1px solid #ccc;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="container">
        <?php if (isset($_SESSION['success'])): ?>
            <div class="success-message">
                <?php 
                    echo $_SESSION['success'];
                    unset($_SESSION['success']);
                ?>
            </div>
        <?php endif; ?>

        <?php if (isset($_SESSION['error'])): ?>
            <div class="error-message">
                <?php 
                    echo $_SESSION['error'];
                    unset($_SESSION['error']);
                ?>
            </div>
        <?php endif; ?>

        <h2>Student Year Level Management</h2>
        <table class="student-table">
            <thead>
                <tr>
                    <th>Student ID</th>
                    <th>Name</th>
                    <th>Current Year Level</th>
                    <th>Change Year Level</th>
                </tr>
            </thead>
            <tbody>
                <?php while ($row = $result->fetch_assoc()): ?>
                <tr>
                    <td><?php echo htmlspecialchars($row['student_id']); ?></td>
                    <td><?php echo htmlspecialchars($row['first_name'] . ' ' . $row['last_name']); ?></td>
                    <td>
                        <span class="current-year">
                            <?php echo htmlspecialchars($row['year_level']); ?>
                        </span>
                    </td>
                    <td>
                        <form method="POST" style="display: inline;">
                            <input type="hidden" name="student_id" value="<?php echo $row['student_id']; ?>">
                            <select name="new_year" required>
                                <option value="Year 1" <?php echo $row['year_level'] === 'Year 1' ? 'selected' : ''; ?>>Year 1</option>
                                <option value="Year 2" <?php echo $row['year_level'] === 'Year 2' ? 'selected' : ''; ?>>Year 2</option>
                                <option value="Year 3" <?php echo $row['year_level'] === 'Year 3' ? 'selected' : ''; ?>>Year 3</option>
                                <option value="Year 4" <?php echo $row['year_level'] === 'Year 4' ? 'selected' : ''; ?>>Year 4</option>
                            </select>
                            <input type="hidden" name="update_year" value="1">
                            <button type="submit" class="year-button">Update</button>
                        </form>
                    </td>
                </tr>
                <?php endwhile; ?>
            </tbody>
        </table>
    </div>
</body>
</html>
