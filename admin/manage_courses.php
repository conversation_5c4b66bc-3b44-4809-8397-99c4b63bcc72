<?php
session_start();

// Check if user is logged in and has registrar role
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'registrar') {
    header("Location: ../public/login.php");
    exit();
}

require_once '../config/db_conn.php';

// Include the course data
include 'course_data.php';

// Add a function to sync courses to database
function syncCoursesToDatabase($courses) {
    global $conn;
    
    try {
        // Begin transaction for database consistency
        $conn->begin_transaction();
        
        // IMPORTANT: First clear ONLY placeholder courses, not ALL courses
        // This prevents legitimate courses from being deleted
        $delete_placeholders = "DELETE FROM courses WHERE course_id LIKE 'PLACEHOLDER%'";
        $conn->query($delete_placeholders);
        
        // Keep track of processed courses to identify any that are only in the database
        $processed_courses = [];
        
        // Now sync all programs, technologies, years and courses
        foreach ($courses as $program => $techs) {
            foreach ($techs as $tech => $years) {
                foreach ($years as $year => $course_list) {
                    $semester = "Semester 1"; // Default semester
                    
                    foreach ($course_list as $course_id => $course_name) {
                        // Determine semester from course_id if possible
                        if (substr($course_id, -1) === "1") {
                            $semester = "Semester 1";
                        } elseif (substr($course_id, -1) === "2") {
                            $semester = "Semester 2";
                        }
                        
                        // Check if course exists
                        $check_sql = "SELECT COUNT(*) as count FROM courses 
                                     WHERE course_id = ? AND program = ? AND technology = ? AND year_level = ?";
                        $check_stmt = $conn->prepare($check_sql);
                        $check_stmt->bind_param("ssss", $course_id, $program, $tech, $year);
                        $check_stmt->execute();
                        $result = $check_stmt->get_result();
                        $count = $result->fetch_assoc()['count'];
                        
                        // Track this course as processed
                        $processed_courses[] = "$program|$tech|$year|$course_id";
                        
                        if ($count == 0) {
                            // Course doesn't exist, insert it
                            $insert_sql = "INSERT INTO courses 
                                           (course_id, course_name, program, technology, year_level, semester) 
                                           VALUES (?, ?, ?, ?, ?, ?)";
                            $insert_stmt = $conn->prepare($insert_sql);
                            $insert_stmt->bind_param("ssssss", 
                                                 $course_id, 
                                                 $course_name, 
                                                 $program, 
                                                 $tech, 
                                                 $year, 
                                                 $semester);
                            $insert_stmt->execute();
                            
                            // Log the insertion for debugging
                            file_put_contents('course_sync.log', date('Y-m-d H:i:s') . " - Added course: $course_id - $course_name\n", FILE_APPEND);
                        } else {
                            // Update existing course to ensure it's up to date
                            $update_sql = "UPDATE courses SET course_name = ?, semester = ? 
                                           WHERE course_id = ? AND program = ? AND technology = ? AND year_level = ?";
                            $update_stmt = $conn->prepare($update_sql);
                            $update_stmt->bind_param("ssssss", 
                                                 $course_name,
                                                 $semester,
                                                 $course_id, 
                                                 $program, 
                                                 $tech, 
                                                 $year);
                            $update_stmt->execute();
                        }
                    }
                }
            }
        }
        
        // Commit the transaction
        $conn->commit();
        
        // Log successful sync for debugging
        file_put_contents('course_sync.log', date('Y-m-d H:i:s') . " - Sync completed successfully\n", FILE_APPEND);
        
        return true;
    } catch (Exception $e) {
        // Rollback changes if there's an error
        $conn->rollback();
        
        // Log the error for debugging
        file_put_contents('course_sync_error.log', date('Y-m-d H:i:s') . " - Error: " . $e->getMessage() . "\n", FILE_APPEND);
        
        return false;
    }
}

// Function to update course_data.php file
function updateCourseDataFile($courses) {
    $content = "<?php\n";
    $content .= '$courses = ' . var_export($courses, true) . ";\n";
    $content .= "?>\n";
    
    // Create a backup with timestamp first
    $backup_filename = 'course_data.php.backup.' . date('Y-m-d-H-i-s');
    file_put_contents($backup_filename, file_get_contents('course_data.php'));
    
    // Use file locking to prevent corruption
    $fp = fopen('course_data.php', 'w');
    if (flock($fp, LOCK_EX)) { // exclusive lock
        fwrite($fp, $content);
        flock($fp, LOCK_UN); // release the lock
    } else {
        // Log if locking failed
        file_put_contents('course_sync_error.log', date('Y-m-d H:i:s') . " - Error: Could not acquire file lock for writing\n", FILE_APPEND);
    }
    fclose($fp);
    
    // Verify that the file was written correctly
    clearstatcache(); // Clear PHP file stat cache
    if (file_exists('course_data.php') && filesize('course_data.php') > 0) {
        // Success - file exists and has content
        file_put_contents('course_sync.log', date('Y-m-d H:i:s') . " - course_data.php updated successfully\n", FILE_APPEND);
    } else {
        // Error - file is empty or doesn't exist
        file_put_contents('course_sync_error.log', date('Y-m-d H:i:s') . " - Error: course_data.php is empty or doesn't exist after write\n", FILE_APPEND);
        // Try to restore from backup
        if (file_exists($backup_filename)) {
            copy($backup_filename, 'course_data.php');
            file_put_contents('course_sync_error.log', date('Y-m-d H:i:s') . " - Restored from backup\n", FILE_APPEND);
        }
    }
}

// Function to update course_outline.php file
function updateCourseOutlineFile($courses) {
    // Read the current file
    $fileContent = file_get_contents('course_outline.php');
    
    // Extract everything before the courses array definition
    $beforeArray = '';
    $startPos = strpos($fileContent, '$courses = [');
    if ($startPos !== false) {
        $beforeArray = substr($fileContent, 0, $startPos);
    } else {
        // If we can't find the array, just log it and return
        error_log("Cannot find courses array in course_outline.php");
        return false;
    }
    
    // Extract everything after the courses array
    $afterArray = '';
    $endPos = strpos($fileContent, '];', $startPos);
    if ($endPos !== false) {
        $afterArray = substr($fileContent, $endPos + 2);
    }
    
    // Create the new content with the updated array
    $newContent = $beforeArray;
    $newContent .= '$courses = ' . var_export($courses, true) . ';';
    $newContent .= $afterArray;
    
    // Write the new content back to the file
    if (file_put_contents('course_outline.php', $newContent)) {
        return true;
    } else {
        error_log("Failed to write to course_outline.php");
        return false;
    }
}

// Fix 3: Add this function to test and ensure data persistence
function verifyCourseData() {
    global $conn, $courses;
    
    // Log verification start
    file_put_contents('course_verify.log', date('Y-m-d H:i:s') . " - Verifying course data consistency\n", FILE_APPEND);
    
    // Get all courses from database
    $db_courses = [];
    $sql = "SELECT program, technology, year_level, course_id, course_name, semester FROM courses 
            WHERE course_id NOT LIKE 'PLACEHOLDER%'";
    $result = $conn->query($sql);
    
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $program = $row['program'];
            $tech = $row['technology'];
            $year = $row['year_level'];
            $course_id = $row['course_id'];
            $course_name = $row['course_name'];
            
            // Build db_courses array mirroring $courses structure
            if (!isset($db_courses[$program])) {
                $db_courses[$program] = [];
            }
            if (!isset($db_courses[$program][$tech])) {
                $db_courses[$program][$tech] = [];
            }
            if (!isset($db_courses[$program][$tech][$year])) {
                $db_courses[$program][$tech][$year] = [];
            }
            
            $db_courses[$program][$tech][$year][$course_id] = $course_name;
        }
    }
    
    // Check for differences and sync if needed
    $sync_needed = false;
    
    // Check if any courses in the file are missing from the database
    foreach ($courses as $program => $techs) {
        foreach ($techs as $tech => $years) {
            foreach ($years as $year => $course_list) {
                foreach ($course_list as $course_id => $course_name) {
                    if (!isset($db_courses[$program][$tech][$year][$course_id])) {
                        $sync_needed = true;
                        file_put_contents('course_verify.log', date('Y-m-d H:i:s') . " - Course in file missing from DB: $program/$tech/$year/$course_id\n", FILE_APPEND);
                    }
                }
            }
        }
    }
    
    // If sync is needed, perform complete sync
    if ($sync_needed) {
        file_put_contents('course_verify.log', date('Y-m-d H:i:s') . " - Database sync required, performing sync...\n", FILE_APPEND);
        syncCoursesToDatabase($courses);
    }
}

// Fix 4: Add this code near the beginning of the file to verify data on page load
// Add after including course_data.php
verifyCourseData();

// At the very top of your file (after session_start())
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Add this debug code at the beginning of your processing section
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Debug info - log form submission data
    error_log("POST submission received: " . print_r($_POST, true));
    
    // Check if action is set
    if (!isset($_POST['action'])) {
        error_log("No action specified in POST data");
    } else {
        error_log("Processing action: " . $_POST['action']);
    }
}

// Main processing logic - place this where you handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get the action from the form
    $action = $_POST['action'] ?? '';
    
    error_log("Form action: $action");
    
    // Process based on action
    if ($action === 'add_program') {
        $program_name = trim($_POST['program_name'] ?? '');
        error_log("Processing add_program: $program_name");
        
        if (empty($program_name)) {
            $_SESSION['error'] = "Program name cannot be empty.";
        } else {
            try {
                // Check if program exists
                $check_sql = "SELECT COUNT(*) as count FROM courses WHERE program = ?";
                $check_stmt = $conn->prepare($check_sql);
                
                if (!$check_stmt) {
                    error_log("Database prepare error: " . $conn->error);
                    $_SESSION['error'] = "Database error: " . $conn->error;
                } else {
                    $check_stmt->bind_param("s", $program_name);
                    $check_stmt->execute();
                    $check_result = $check_stmt->get_result();
                    $count_row = $check_result->fetch_assoc();
                    
                    if ($count_row['count'] > 0) {
                        $_SESSION['error'] = "Program '$program_name' already exists.";
                    } else {
                        // Add placeholder course to establish program
                        $sql = "INSERT INTO courses (course_id, course_name, program, technology, year_level, semester) 
                                VALUES ('TEMP001', 'Program Created', ?, 'General', 'Year 1', 'Semester 1')";
                        $stmt = $conn->prepare($sql);
                        
                        if (!$stmt) {
                            error_log("Database prepare error: " . $conn->error);
                            $_SESSION['error'] = "Database error: " . $conn->error;
                        } else {
                            $stmt->bind_param("s", $program_name);
                            
                            if ($stmt->execute()) {
                                $_SESSION['message'] = "Program '$program_name' added successfully.";
                            } else {
                                error_log("Execute error: " . $stmt->error);
                                $_SESSION['error'] = "Failed to add program: " . $stmt->error;
                            }
                        }
                    }
                }
            } catch (Exception $e) {
                error_log("Exception: " . $e->getMessage());
                $_SESSION['error'] = "Database error: " . $e->getMessage();
            }
        }
        
        // Always redirect after processing
        header("Location: manage_courses.php?tab=add");
        exit();
    }
    
    // Similar pattern for other form actions
    elseif ($action === 'add_tech') {
        $program = trim($_POST['program'] ?? '');
        $tech_name = trim($_POST['tech_name'] ?? '');
        error_log("Processing add_tech: $program, $tech_name");
        
        if (empty($program) || empty($tech_name)) {
            $_SESSION['error'] = "Both program and technology name are required.";
        } else {
            try {
                // Check if tech exists for this program
                $check_sql = "SELECT COUNT(*) as count FROM courses WHERE program = ? AND technology = ?";
                $check_stmt = $conn->prepare($check_sql);
                $check_stmt->bind_param("ss", $program, $tech_name);
                $check_stmt->execute();
                $check_result = $check_stmt->get_result();
                $count_row = $check_result->fetch_assoc();
                
                if ($count_row['count'] > 0) {
                    $_SESSION['error'] = "Technology '$tech_name' already exists for program '$program'.";
                } else {
                    // Add placeholder course
                    $sql = "INSERT INTO courses (course_id, course_name, program, technology, year_level, semester) 
                            VALUES ('TEMP002', 'Technology Created', ?, ?, 'Year 1', 'Semester 1')";
                    $stmt = $conn->prepare($sql);
                    $stmt->bind_param("ss", $program, $tech_name);
                    
                    if ($stmt->execute()) {
                        $_SESSION['message'] = "Technology '$tech_name' added successfully to program '$program'.";
                    } else {
                        $_SESSION['error'] = "Failed to add technology: " . $stmt->error;
                    }
                }
            } catch (Exception $e) {
                $_SESSION['error'] = "Database error: " . $e->getMessage();
            }
        }
        
        header("Location: manage_courses.php?tab=add");
        exit();
    }
    
    // Add year level
    elseif ($action === 'add_year') {
        $program = trim($_POST['program'] ?? '');
        $tech = trim($_POST['tech'] ?? '');
        $year_name = trim($_POST['year_name'] ?? '');
        error_log("Processing add_year: $program, $tech, $year_name");
        
        if (empty($program) || empty($tech) || empty($year_name)) {
            $_SESSION['error'] = "Program, technology, and year level are all required.";
        } else {
            try {
                // Begin transaction for adding year level entries
                $conn->begin_transaction();
                
                // Add year level for Semester 1
                $sql1 = "INSERT INTO courses (course_id, course_name, program, technology, year_level, semester) 
                        VALUES (CONCAT('PH', ?, '1'), 'Year Level Created', ?, ?, ?, 'Semester 1')";
                $stmt1 = $conn->prepare($sql1);
                $placeholder = substr($year_name, -1); // Get the year number from "Year X"
                $stmt1->bind_param("ssss", $placeholder, $program, $tech, $year_name);
                $stmt1->execute();
                
                // Add year level for Semester 2
                $sql2 = "INSERT INTO courses (course_id, course_name, program, technology, year_level, semester) 
                        VALUES (CONCAT('PH', ?, '2'), 'Year Level Created', ?, ?, ?, 'Semester 2')";
                $stmt2 = $conn->prepare($sql2);
                $stmt2->bind_param("ssss", $placeholder, $program, $tech, $year_name);
                $stmt2->execute();
                
                // Commit transaction
                $conn->commit();
                
                $_SESSION['message'] = "Year level '$year_name' added successfully to program '$program' and technology '$tech'.";
            } catch (Exception $e) {
                // Rollback transaction on error
                $conn->rollback();
                $_SESSION['error'] = "Database error: " . $e->getMessage();
                error_log("Add year level error: " . $e->getMessage());
            }
        }
        
        header("Location: manage_courses.php?tab=add");
        exit();
    }
    
    // Add course
    elseif ($action === 'add_course') {
        $program = trim($_POST['program'] ?? '');
        $tech = trim($_POST['tech'] ?? '');
        $year = trim($_POST['year'] ?? '');
        $course_code = trim($_POST['course_code'] ?? '');
        $course_name = trim($_POST['course_name'] ?? '');
        $semester = $_POST['semester'] ?? 'Semester 1';
        error_log("Processing add_course: $program, $tech, $year, $course_code, $course_name, $semester");
        
        if (empty($program) || empty($tech) || empty($year) || empty($course_code) || empty($course_name)) {
            $_SESSION['error'] = "All fields are required.";
        } else {
            try {
                // Add the course
                $sql = "INSERT INTO courses (course_id, course_name, program, technology, year_level, semester) 
                        VALUES (?, ?, ?, ?, ?, ?)";
                $stmt = $conn->prepare($sql);
                $stmt->bind_param("ssssss", $course_code, $course_name, $program, $tech, $year, $semester);
                
                if ($stmt->execute()) {
                    $_SESSION['message'] = "Course '$course_code: $course_name' added successfully.";
                } else {
                    $_SESSION['error'] = "Failed to add course: " . $stmt->error;
                }
            } catch (Exception $e) {
                $_SESSION['error'] = "Database error: " . $e->getMessage();
            }
        }
        
        header("Location: manage_courses.php?tab=add");
        exit();
    }
    
    elseif ($action === 'fix_year_levels') {
        $program = trim($_POST['program'] ?? '');
        $tech = trim($_POST['tech'] ?? '');
        $year_name = trim($_POST['year'] ?? '');
        
        if (empty($program) || empty($tech) || empty($year_name)) {
            $_SESSION['error'] = "Program, technology, and year level are all required for fixing.";
        } else {
            try {
                // Begin transaction
                $conn->begin_transaction();
                
                // Check if we have any courses for this year
                $check_sql = "SELECT COUNT(*) as count FROM courses 
                              WHERE program = ? AND technology = ? AND year_level = ?";
                $check_stmt = $conn->prepare($check_sql);
                $check_stmt->bind_param("sss", $program, $tech, $year_name);
                $check_stmt->execute();
                $check_result = $check_stmt->get_result();
                $count = $check_result->fetch_assoc()['count'];
                
                if ($count == 0) {
                    // No existing courses for this year - create placeholder entries
                    $placeholder = substr($year_name, -1); // Get the year number from "Year X"
                    
                    // Add year level entry for Semester 1
                    $sql1 = "INSERT INTO courses (course_id, course_name, program, technology, year_level, semester) 
                            VALUES (CONCAT('PH', ?, '1'), 'Year Level Entry', ?, ?, ?, 'Semester 1')";
                    $stmt1 = $conn->prepare($sql1);
                    $stmt1->bind_param("ssss", $placeholder, $program, $tech, $year_name);
                    $stmt1->execute();
                    
                    // Add year level entry for Semester 2
                    $sql2 = "INSERT INTO courses (course_id, course_name, program, technology, year_level, semester) 
                            VALUES (CONCAT('PH', ?, '2'), 'Year Level Entry', ?, ?, ?, 'Semester 2')";
                    $stmt2 = $conn->prepare($sql2);
                    $stmt2->bind_param("ssss", $placeholder, $program, $tech, $year_name);
                    $stmt2->execute();
                    
                    // Commit transaction
                    $conn->commit();
                    
                    $_SESSION['message'] = "Fixed: Created year level '$year_name' entries for program '$program' and technology '$tech'.";
                } else {
                    // Year level already exists
                    $_SESSION['message'] = "Year level '$year_name' already exists for this program and technology.";
                    $conn->commit();
                }
            } catch (Exception $e) {
                $conn->rollback();
                $_SESSION['error'] = "Error fixing year level: " . $e->getMessage();
                error_log("Error fixing year level: " . $e->getMessage());
            }
        }
        
        header("Location: manage_courses.php?tab=add");
        exit();
    }
}

?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Courses - DBTI Online Registration</title>
    <link rel="stylesheet" href="../public/assets/css/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.1/css/all.min.css" />
    <link rel="icon" href="img/logo.webp" type="image/png">
    <style>
        /* Additional styles to complement styles.css */
        .page-header {
            text-align: center;
            padding: 30px 0;
            background-color: #ffbf00;
            color: white;
            margin-bottom: 30px;
            border-radius: 0 0 12px 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .page-header h1 {
            font-size: 2.5rem;
            margin: 0;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
        }
        
        .page-header p {
            font-size: 1.2rem;
            margin-top: 10px;
            opacity: 0.9;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto 80px;
            padding: 0 20px;
        }
        
        .card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            padding: 25px;
            margin-bottom: 25px;
            border-left: 5px solid #ffbf00;
            overflow: hidden;
        }
        
        .message {
            padding: 15px;
            margin-bottom: 25px;
            border-radius: 8px;
            font-weight: 500;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            border-left: 5px solid #28a745;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            border-left: 5px solid #dc3545;
        }
        
        /* Tab styles */
        .tabs {
            display: flex;
            margin-bottom: 30px;
            border-radius: 8px;
            overflow: hidden;
            background: white;
            border: 1px solid #eee;
        }
        
        .tab {
            flex: 1;
            padding: 15px 20px;
            text-align: center;
            cursor: pointer;
            font-weight: 600;
            color: #555;
            transition: all 0.3s ease;
            border-bottom: 3px solid transparent;
        }
        
        .tab:hover {
            background: #f9f9f9;
            color: #333;
        }
        
        .tab.active {
            background: white;
            color: #4CAF50;
            border-bottom: 3px solid #4CAF50;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        /* Form layout */
        .form-container {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 25px;
        }
        
        .form-card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.06);
            border-top: 5px solid #ffbf00;
        }
        
        .form-card h3 {
            color: #333;
            margin-top: 0;
            margin-bottom: 20px;
            border-bottom: 2px solid #f0f0f0;
            padding-bottom: 10px;
        }
        
        /* Course hierarchy display */
        .course-hierarchy {
            max-height: 70vh;
            overflow-y: auto;
            padding-right: 10px;
            border: 1px solid #eee;
            border-radius: 8px;
            background: #fcfcfc;
        }
        
        .program-item, .tech-item, .year-item {
            margin-bottom: 10px;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .program-header, .tech-header, .year-header {
            background: #f8f8f8;
            padding: 12px 15px;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #eee;
            transition: background-color 0.3s;
        }
        
        .program-header:hover, .tech-header:hover, .year-header:hover {
            background: #f0f0f0;
        }
        
        .program-header {
            background: #ffbf00;
            color: white;
        }
        
        .tech-header {
            background: #4CAF50;
            color: white;
            margin-left: 15px;
        }
        
        .year-header {
            background: #2196F3;
            color: white;
            margin-left: 30px;
        }
        
        .content-section {
            display: none;
            padding: 10px 0;
        }
        
        .content-section.active {
            display: block;
        }
        
        .course-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 15px;
            margin: 5px 45px;
            background: white;
            border-radius: 6px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        
        .course-actions {
            display: flex;
            gap: 10px;
        }
        
        .action-btn {
            border: none;
            padding: 8px 12px;
            border-radius: 4px;
            color: white;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .edit-btn {
            background: #2196F3;
        }
        
        .edit-btn:hover {
            background: #0b7dda;
        }
        
        .delete-btn {
            background: #f44336;
        }
        
        .delete-btn:hover {
            background: #d32f2f;
        }
        
        .collapse-indicator {
            margin-right: 10px;
            transition: transform 0.3s;
        }
        
        .collapsed .collapse-indicator {
            transform: rotate(-90deg);
        }
        
        /* Modal styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.4);
        }
        
        .modal-content {
            background-color: #fefefe;
            margin: 10% auto;
            padding: 20px;
            border: 1px solid #888;
            width: 80%;
            max-width: 500px;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            animation: modalfade 0.3s;
        }
        
        @keyframes modalfade {
            from {opacity: 0; transform: translateY(-30px);}
            to {opacity: 1; transform: translateY(0);}
        }
        
        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        
        .close:hover {
            color: #333;
        }
        
        .hint {
            display: block;
            font-size: 12px;
            color: #666;
            margin-top: 4px;
        }
        
        /* Add semester-specific styling */
        .semester-1 {
            background-color: #e1f5fe;
            border-left: 4px solid #03a9f4;
        }
        
        .semester-2 {
            background-color: #e8f5e9;
            border-left: 4px solid #4caf50;
        }
        
        .semester-header {
            padding: 10px 15px;
            cursor: pointer;
            border-radius: 4px;
            margin: 5px 0;
        }
        
        .semester-content {
            padding-left: 15px;
            border-left: 1px dashed #ccc;
            margin-left: 15px;
        }
        
        /* Responsive layout */
        @media screen and (max-width: 992px) {
            .form-container {
                grid-template-columns: 1fr;
            }
            
            .tech-header {
                margin-left: 10px;
            }
            
            .year-header {
                margin-left: 20px;
            }
            
            .course-item {
                margin: 5px 30px;
                flex-direction: column;
                align-items: stretch;
            }
            
            .course-actions {
                margin-top: 15px;
            }
            
            .modal-content {
                width: 90%;
                margin: 20% auto;
            }
        }
        
        @media screen and (max-width: 768px) {
            .course-hierarchy {
                max-height: 500px;
            }
            
            .course-actions {
                flex-direction: column;
                gap: 5px;
            }
            
            .action-btn {
                width: 100%;
            }
        }
        
        /* Hamburger menu styles */
        .hamburger {
            display: none;
            flex-direction: column;
            cursor: pointer;
            padding: 5px;
            z-index: 1001;
        }
        
        .bar {
            width: 25px;
            height: 3px;
            background-color: white;
            margin: 3px 0;
            transition: 0.4s;
        }
        
        /* Mobile navigation styles */
        @media screen and (max-width: 768px) {
            .hamburger {
                display: flex;
            }
            
            nav ul {
                display: none;
                flex-direction: column;
                position: fixed;
                top: 0;
                right: -300px;
                width: 250px;
                height: 100vh;
                background-color: #2c3e50;
                padding-top: 60px;
                transition: right 0.3s ease;
                z-index: 1000;
                box-shadow: -5px 0 15px rgba(0,0,0,0.2);
            }
            
            nav ul.active {
                display: flex;
                right: 0;
            }
            
            nav ul li {
                margin: 0;
                width: 100%;
            }
            
            nav ul li a {
                display: block;
                padding: 15px 25px;
                width: 100%;
                text-align: left;
                border-bottom: 1px solid rgba(255,255,255,0.1);
            }
        }
        
        /* Green Color Palette */
        :root {
            --green-50: #f0fdf4;
            --green-100: #dcfce7;
            --green-200: #bbf7d0;
            --green-300: #86efac;
            --green-400: #4ade80;
            --green-500: #22c55e;
            --green-600: #16a34a;
            --green-700: #15803d;
            --green-800: #166534;
            --green-900: #14532d;
            --green-950: #052e16;
        }
        
        /* Improved nested dropdowns with green theme */
        .program-section {
            margin-bottom: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 6px rgba(0,0,0,0.1);
            background-color: white;
            overflow: hidden;
        }
        
        .program-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            background-color: var(--green-600);
            cursor: pointer;
            transition: background-color 0.3s;
            color: white;
        }
        
        .program-header:hover {
            background-color: var(--green-700);
        }
        
        .program-header h3 {
            margin: 0;
            font-size: 1.2rem;
            font-weight: 600;
        }
        
        .program-content {
            background-color: var(--green-50);
            padding: 0;
            border-top: 1px solid var(--green-200);
        }
        
        /* Technology section (first nested level) */
        .tech-section {
            margin: 10px;
            border-radius: 6px;
            box-shadow: 0 1px 4px rgba(0,0,0,0.05);
            overflow: hidden;
        }
        
        .tech-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 15px;
            background-color: var(--green-500);
            cursor: pointer;
            color: white;
        }
        
        .tech-header:hover {
            background-color: var(--green-600);
        }
        
        .tech-header h4 {
            margin: 0;
            font-size: 1.1rem;
        }
        
        .tech-content {
            background-color: var(--green-100);
            padding: 0;
        }
        
        /* Year section (second nested level) */
        .year-section {
            margin: 8px;
            border-radius: 6px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
            overflow: hidden;
        }
        
        .year-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 15px;
            background-color: var(--green-400);
            cursor: pointer;
            color: white;
        }
        
        .year-header:hover {
            background-color: var(--green-500);
        }
        
        .year-header h5 {
            margin: 0;
            font-size: 1rem;
        }
        
        .year-content {
            background-color: white;
            padding: 0;
        }
        
        /* Semester section (third nested level) */
        .semester-section {
            margin: 6px;
            border-radius: 6px;
            overflow: hidden;
        }
        
        .semester-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 12px;
            background-color: var(--green-300);
            cursor: pointer;
            color: var(--green-900);
        }
        
        .semester-header:hover {
            background-color: var(--green-400);
        }
        
        .semester-header h6 {
            margin: 0;
            font-size: 0.95rem;
            font-weight: 600;
        }
        
        .semester-content {
            background-color: var(--green-50);
            padding: 10px;
        }
        
        /* Course list */
        .course-list {
            padding-left: 0;
            list-style: none;
            margin: 0;
        }
        
        .course-list li {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            margin-bottom: 5px;
            background-color: white;
            border-radius: 4px;
            box-shadow: 0 1px 2px rgba(0,0,0,0.05);
            border-left: 3px solid var(--green-400);
        }
        
        .course-list li:hover {
            background-color: var(--green-50);
        }
        
        .course-code {
            font-weight: bold;
            color: var(--green-800);
            min-width: 80px;
        }
        
        .course-name {
            flex-grow: 1;
            padding: 0 15px;
            color: var(--green-900);
        }
        
        .course-actions {
            display: flex;
            gap: 5px;
        }
        
        .edit-btn, .delete-btn {
            border: none;
            background: none;
            cursor: pointer;
            padding: 5px;
            border-radius: 4px;
            transition: all 0.2s;
        }
        
        .edit-btn {
            color: var(--green-600);
        }
        
        .edit-btn:hover {
            background-color: var(--green-100);
        }
        
        .delete-btn {
            color: #ef4444;
        }
        
        .delete-btn:hover {
            background-color: #fee2e2;
        }
        
        .no-courses {
            padding: 10px;
            color: var(--green-500);
            font-style: italic;
        }
        
        /* Collapse indicator animation */
        .collapse-indicator {
            transition: transform 0.3s;
        }
        
        .rotate-down {
            transform: rotate(90deg);
        }
        
        .message-container {
            margin-bottom: 20px;
        }
        
        .success-message {
            padding: 12px 20px;
            background-color: #d1fae5;
            color: #065f46;
            border-left: 4px solid #059669;
            border-radius: 4px;
            margin-bottom: 15px;
        }
        
        .error-message {
            padding: 12px 20px;
            background-color: #fee2e2;
            color: #991b1b;
            border-left: 4px solid #ef4444;
            border-radius: 4px;
            margin-bottom: 15px;
        }
        
        .success-message i, .error-message i {
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <nav>
        <div class="logo">
            <span class="heading">DBTI Course Management</span>
        </div>
        
        <!-- Hamburger icon for mobile -->
        <div class="hamburger" onclick="toggleNavbar()">
            <div class="bar"></div>
            <div class="bar"></div>
            <div class="bar"></div>
        </div>
        
        <ul id="navbar">
            <li><a href="registrar_dashboard.php"><i class="fas fa-home"></i> Dashboard</a></li>
            <li><a href="registered_students.php"><i class="fas fa-users"></i> View Students</a></li>
            <li><a href="manage_courses.php" class="active"><i class="fas fa-book"></i> Manage Courses</a></li>
            <li><a href="../public/logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
        </ul>
    </nav>

    <!-- Page Header -->
    <div class="page-header">
        <h1>Manage Courses and Programs</h1>
        <p>Add, edit, or delete programs, technologies, years, and courses</p>
    </div>

    <!-- Main Content Container -->
    <div class="container">
        <!-- Update message display section at the top of your content -->
        <div class="message-container">
            <?php if (isset($_SESSION['message'])): ?>
                <div class="success-message">
                    <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($_SESSION['message']); ?>
                </div>
                <?php unset($_SESSION['message']); ?>
            <?php endif; ?>
            
            <?php if (isset($_SESSION['error'])): ?>
                <div class="error-message">
                    <i class="fas fa-exclamation-circle"></i> <?php echo htmlspecialchars($_SESSION['error']); ?>
                </div>
                <?php unset($_SESSION['error']); ?>
            <?php endif; ?>
        </div>
        
        <div class="card">
            <div class="tabs">
                <div class="tab active" onclick="showTab('add')">
                    <i class="fas fa-plus-circle"></i> Add New
                </div>
                <div class="tab" onclick="showTab('view')">
                    <i class="fas fa-list"></i> View Courses
                </div>
            </div>
            
            <div id="add" class="tab-content active">
                <div class="form-container">
                    <div class="form-card">
                        <h3><i class="fas fa-graduation-cap"></i> Add Program</h3>
                        <form method="post">
                            <input type="hidden" name="action" value="add_program">
                            <div class="form-group">
                                <label for="program_name">Program Name:</label>
                                <input type="text" id="program_name" name="program_name" placeholder="e.g., Bachelor in Technology" required>
                            </div>
                            <button type="submit" class="btn"><i class="fas fa-plus"></i> Add Program</button>
                        </form>
                    </div>
                    
                    <div class="form-card">
                        <h3><i class="fas fa-cogs"></i> Add Technology</h3>
                        <form method="post">
                            <input type="hidden" name="action" value="add_tech">
                            <div class="form-group">
                                <label for="program_tech">Program:</label>
                                <select id="program_tech" name="program" required>
                                    <option value="">Select Program</option>
                                    <?php
                                    // Fetch all distinct programs directly from the database
                                    $program_tech_query = "SELECT DISTINCT program FROM courses ORDER BY program";
                                    $program_tech_result = $conn->query($program_tech_query);
                                    
                                    if ($program_tech_result && $program_tech_result->num_rows > 0) {
                                        while ($program_row = $program_tech_result->fetch_assoc()) {
                                            $program_name = $program_row['program'];
                                            echo "<option value=\"" . htmlspecialchars($program_name) . "\">" . htmlspecialchars($program_name) . "</option>";
                                        }
                                    }
                                    ?>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="tech_name">Technology Name:</label>
                                <input type="text" id="tech_name" name="tech_name" placeholder="e.g., Information Technology" required>
                            </div>
                            <button type="submit" class="btn"><i class="fas fa-plus"></i> Add Technology</button>
                        </form>
                    </div>
                    
                    <div class="form-card">
                        <h3><i class="fas fa-calendar-alt"></i> Add Year Level</h3>
                        <form method="post">
                            <input type="hidden" name="action" value="add_year">
                            <div class="form-group">
                                <label for="program_year">Program:</label>
                                <select id="program_year" name="program" required onchange="loadTechs(this.value, 'tech_year')">
                                    <option value="">Select Program</option>
                                    <?php
                                    // Fetch all distinct programs directly from the database
                                    $program_year_query = "SELECT DISTINCT program FROM courses ORDER BY program";
                                    $program_year_result = $conn->query($program_year_query);
                                    
                                    if ($program_year_result && $program_year_result->num_rows > 0) {
                                        while ($program_row = $program_year_result->fetch_assoc()) {
                                            $program_name = $program_row['program'];
                                            echo "<option value=\"" . htmlspecialchars($program_name) . "\">" . htmlspecialchars($program_name) . "</option>";
                                        }
                                    }
                                    ?>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="tech_year">Technology:</label>
                                <select id="tech_year" name="tech" required>
                                    <option value="">Select Program First</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="year_name">Year Level:</label>
                                <select id="year_name" name="year_name" required>
                                    <option value="">Select Year Level</option>
                                    <option value="Year 1">Year 1</option>
                                    <option value="Year 2">Year 2</option>
                                    <option value="Year 3">Year 3</option>
                                    <option value="Year 4">Year 4</option>
                                </select>
                            </div>
                            <button type="submit" class="btn"><i class="fas fa-plus"></i> Add Year Level</button>
                        </form>
                    </div>
                    
                    <div class="form-card">
                        <h3><i class="fas fa-wrench"></i> Repair Year Levels</h3>
                        <p>If you're having trouble with year levels not appearing, use this tool to fix them.</p>
                        <form method="post">
                            <input type="hidden" name="action" value="fix_year_levels">
                            <div class="form-group">
                                <label for="fix_program">Program:</label>
                                <select id="fix_program" name="program" required onchange="loadTechs(this.value, 'fix_tech')">
                                    <option value="">Select Program</option>
                                    <?php
                                    // Fetch all distinct programs from the database
                                    $fix_program_query = "SELECT DISTINCT program FROM courses ORDER BY program";
                                    $fix_program_result = $conn->query($fix_program_query);
                                    
                                    if ($fix_program_result && $fix_program_result->num_rows > 0) {
                                        while ($program_row = $fix_program_result->fetch_assoc()) {
                                            $program_name = $program_row['program'];
                                            echo "<option value=\"" . htmlspecialchars($program_name) . "\">" . htmlspecialchars($program_name) . "</option>";
                                        }
                                    }
                                    ?>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="fix_tech">Technology:</label>
                                <select id="fix_tech" name="tech" required>
                                    <option value="">Select Program First</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="fix_year">Year Level to Fix:</label>
                                <select id="fix_year" name="year" required>
                                    <option value="">Select Year Level</option>
                                    <option value="Year 1">Year 1</option>
                                    <option value="Year 2">Year 2</option>
                                    <option value="Year 3">Year 3</option>
                                    <option value="Year 4">Year 4</option>
                                </select>
                            </div>
                            <button type="submit" class="btn"><i class="fas fa-hammer"></i> Fix Year Level</button>
                        </form>
                    </div>
                    
                    <div class="form-card">
                        <h3><i class="fas fa-book"></i> Add Course</h3>
                        <form method="post">
                            <input type="hidden" name="action" value="add_course">
                            <div class="form-group">
                                <label for="program_course">Program:</label>
                                <select id="program_course" name="program" required onchange="loadTechs(this.value, 'tech_course')">
                                    <option value="">Select Program</option>
                                    <?php
                                    // Fetch all distinct programs directly from the database
                                    $program_query = "SELECT DISTINCT program FROM courses ORDER BY program";
                                    $program_result = $conn->query($program_query);
                                    
                                    if ($program_result && $program_result->num_rows > 0) {
                                        while ($program_row = $program_result->fetch_assoc()) {
                                            $program_name = $program_row['program'];
                                            echo "<option value=\"" . htmlspecialchars($program_name) . "\">" . htmlspecialchars($program_name) . "</option>";
                                        }
                                    }
                                    ?>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="tech_course">Technology:</label>
                                <select id="tech_course" name="tech" required onchange="loadYears()">
                                    <option value="">Select Program First</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="year_course">Year:</label>
                                <select id="year_course" name="year" required>
                                    <option value="">Select Technology First</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="semester">Semester:</label>
                                <select id="semester" name="semester" required>
                                    <option value="Semester 1">Semester 1</option>
                                    <option value="Semester 2">Semester 2</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="course_code">Course Code:</label>
                                <input type="text" id="course_code" name="course_code" placeholder="e.g., INF121" required>
                                <small>Note: The last digit should match the semester (1 for Semester 1, 2 for Semester 2)</small>
                            </div>
                            <div class="form-group">
                                <label for="course_name">Course Name:</label>
                                <input type="text" id="course_name" name="course_name" placeholder="e.g., Introduction to Programming (3 units)" required>
                            </div>
                            <button type="submit" class="btn"><i class="fas fa-plus"></i> Add Course</button>
                        </form>
                    </div>
                </div>
            </div>
            
            <div id="view" class="tab-content">
                <h2><i class="fas fa-eye"></i> View Course Structure</h2>
                
                <?php
                // Get all distinct programs directly from database
                $programs_query = "SELECT DISTINCT program FROM courses ORDER BY program";
                $programs_result = $conn->query($programs_query);
                $all_programs = array();
                
                while ($program_row = $programs_result->fetch_assoc()) {
                    $all_programs[] = $program_row['program'];
                }
                
                // Loop through each program
                foreach ($all_programs as $index => $program):
                    $program_id = 'program-' . $index;
                    
                    // Get technologies for this program
                    $techs_query = "SELECT DISTINCT technology FROM courses WHERE program = ? ORDER BY technology";
                    $techs_stmt = $conn->prepare($techs_query);
                    $techs_stmt->bind_param("s", $program);
                    $techs_stmt->execute();
                    $techs_result = $techs_stmt->get_result();
                    
                    // Start program section
                    ?>
                    <div class="program-section" id="<?php echo $program_id; ?>">
                        <div class="program-header" onclick="toggleDropdown('<?php echo $program_id; ?>-content')">
                            <h3><?php echo htmlspecialchars($program); ?></h3>
                            <i class="fas fa-chevron-right collapse-indicator" id="<?php echo $program_id; ?>-indicator"></i>
                        </div>
                        <div class="program-content" id="<?php echo $program_id; ?>-content" style="display: none;">
                            <?php
                            // Loop through each technology for this program
                            $tech_index = 0;
                            while ($tech_row = $techs_result->fetch_assoc()):
                                $tech = $tech_row['technology'];
                                $tech_id = $program_id . '-tech-' . $tech_index;
                                
                                // Get years for this program and technology
                                $years_query = "SELECT DISTINCT year_level FROM courses WHERE program = ? AND technology = ? ORDER BY year_level";
                                $years_stmt = $conn->prepare($years_query);
                                $years_stmt->bind_param("ss", $program, $tech);
                                $years_stmt->execute();
                                $years_result = $years_stmt->get_result();
                                ?>
                                
                                <div class="tech-section" id="<?php echo $tech_id; ?>">
                                    <div class="tech-header" onclick="toggleDropdown('<?php echo $tech_id; ?>-content')">
                                        <h4><?php echo htmlspecialchars($tech); ?></h4>
                                        <i class="fas fa-chevron-right collapse-indicator" id="<?php echo $tech_id; ?>-indicator"></i>
                                    </div>
                                    <div class="tech-content" id="<?php echo $tech_id; ?>-content" style="display: none;">
                                        <?php
                                        // Loop through each year for this program and technology
                                        $year_index = 0;
                                        while ($year_row = $years_result->fetch_assoc()):
                                            $year = $year_row['year_level'];
                                            $year_id = $tech_id . '-year-' . $year_index;
                                            
                                            // Get courses for this program, technology, and year
                                            $courses_query = "SELECT course_id, course_name, semester FROM courses 
                                                             WHERE program = ? AND technology = ? AND year_level = ? 
                                                             ORDER BY semester, course_id";
                                            $courses_stmt = $conn->prepare($courses_query);
                                            $courses_stmt->bind_param("sss", $program, $tech, $year);
                                            $courses_stmt->execute();
                                            $courses_result = $courses_stmt->get_result();
                                            
                                            // Group courses by semester
                                            $semester_courses = array(
                                                'Semester 1' => array(),
                                                'Semester 2' => array()
                                            );
                                            
                                            while ($course = $courses_result->fetch_assoc()) {
                                                $semester_courses[$course['semester']][] = $course;
                                            }
                                            ?>
                                            
                                            <div class="year-section" id="<?php echo $year_id; ?>">
                                                <div class="year-header" onclick="toggleDropdown('<?php echo $year_id; ?>-content')">
                                                    <h5><?php echo htmlspecialchars($year); ?></h5>
                                                    <i class="fas fa-chevron-right collapse-indicator" id="<?php echo $year_id; ?>-indicator"></i>
                                                </div>
                                                <div class="year-content" id="<?php echo $year_id; ?>-content" style="display: none;">
                                                    
                                                    <?php 
                                                    $semester_index = 0;
                                                    foreach ($semester_courses as $semester => $semester_course_list): 
                                                        $semester_id = $year_id . '-sem-' . $semester_index;
                                                    ?>
                                                        <div class="semester-section" id="<?php echo $semester_id; ?>">
                                                            <div class="semester-header" onclick="toggleDropdown('<?php echo $semester_id; ?>-content')">
                                                                <h6><?php echo htmlspecialchars($semester); ?></h6>
                                                                <i class="fas fa-chevron-right collapse-indicator" id="<?php echo $semester_id; ?>-indicator"></i>
                                                            </div>
                                                            <div class="semester-content" id="<?php echo $semester_id; ?>-content" style="display: none;">
                                                                <?php if (empty($semester_course_list)): ?>
                                                                    <p class="no-courses">No courses found for this semester.</p>
                                                                <?php else: ?>
                                                                    <ul class="course-list">
                                                                        <?php foreach ($semester_course_list as $course): ?>
                                                                            <li>
                                                                                <span class="course-code"><?php echo htmlspecialchars($course['course_id']); ?></span>
                                                                                <span class="course-name"><?php echo htmlspecialchars($course['course_name']); ?></span>
                                                                                <div class="course-actions">
                                                                                    <button class="edit-btn" onclick="openEditModal('<?php echo htmlspecialchars($program); ?>', '<?php echo htmlspecialchars($tech); ?>', '<?php echo htmlspecialchars($year); ?>', '<?php echo htmlspecialchars($course['course_id']); ?>', '<?php echo htmlspecialchars($course['course_name']); ?>')">
                                                                                        <i class="fas fa-edit"></i>
                                                                                    </button>
                                                                                    <form class="delete-form" method="post" onsubmit="return confirm('Are you sure you want to delete this course?');">
                                                                                        <input type="hidden" name="action" value="delete_course">
                                                                                        <input type="hidden" name="course_id" value="<?php echo htmlspecialchars($course['course_id']); ?>">
                                                                                        <button type="submit" class="delete-btn">
                                                                                            <i class="fas fa-trash-alt"></i>
                                                                                        </button>
                                                                                    </form>
                                                                                </div>
                                                                            </li>
                                                                        <?php endforeach; ?>
                                                                    </ul>
                                                                <?php endif; ?>
                                                            </div>
                                                        </div>
                                                    <?php 
                                                    $semester_index++;
                                                    endforeach; 
                                                    ?>
                                                </div>
                                            </div>
                                        <?php 
                                        $year_index++;
                                        endwhile; 
                                        ?>
                                    </div>
                                </div>
                            <?php 
                            $tech_index++;
                            endwhile; 
                            ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
    
    <!-- Edit Course Modal -->
    <div id="editModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeEditModal()">&times;</span>
            <h2><i class="fas fa-edit"></i> Edit Course</h2>
            <form method="post" id="editCourseForm">
                <input type="hidden" name="action" value="edit_course">
                <input type="hidden" id="edit_program" name="program">
                <input type="hidden" id="edit_tech" name="tech">
                <input type="hidden" id="edit_year" name="year">
                <input type="hidden" id="original_course_code" name="original_course_code">
                
                <div class="form-group">
                    <label for="edit_course_code">Course Code:</label>
                    <input type="text" id="edit_course_code" name="course_code" required>
                </div>
                
                <div class="form-group">
                    <label for="edit_course_name">Course Name:</label>
                    <input type="text" id="edit_course_name" name="new_course_name" required>
                </div>
                
                <div class="form-group">
                    <label for="edit_semester">Semester:</label>
                    <select id="edit_semester" name="semester">
                        <option value="Semester 1">Semester 1</option>
                        <option value="Semester 2">Semester 2</option>
                    </select>
                    <span class="hint">You can manually edit the course code or let the semester update it automatically.</span>
                </div>
                
                <button type="submit" class="btn"><i class="fas fa-save"></i> Update Course</button>
            </form>
        </div>
    </div>

    <script>
        // Toggle navbar for mobile
        function toggleNavbar() {
            const navbar = document.getElementById('navbar');
            navbar.classList.toggle('active');
        }
        
        // Tab functionality
        function showTab(tabId) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // Deactivate all tabs
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Activate selected tab and content
            document.getElementById(tabId).classList.add('active');
            document.querySelector(`.tab[onclick="showTab('${tabId}')"]`).classList.add('active');
        }
        
        // Unified dropdown toggle function
        function toggleDropdown(contentId) {
            const content = document.getElementById(contentId);
            const indicator = document.getElementById(contentId.replace('-content', '-indicator'));
            
            if (content.style.display === 'none' || !content.style.display) {
                content.style.display = 'block';
                indicator.classList.add('rotate-down');
                // Save state to localStorage
                localStorage.setItem(contentId + '-state', 'open');
            } else {
                content.style.display = 'none';
                indicator.classList.remove('rotate-down');
                // Save state to localStorage
                localStorage.setItem(contentId + '-state', 'closed');
            }
            
            // Prevent event from bubbling up to parent dropdowns
            event.stopPropagation();
        }
        
        // Initialize dropdown states on page load
        document.addEventListener('DOMContentLoaded', function() {
            // Find all dropdown content elements
            const dropdownContents = document.querySelectorAll('[id$="-content"]');
            
            dropdownContents.forEach(content => {
                const contentId = content.id;
                const indicatorId = contentId.replace('-content', '-indicator');
                const indicator = document.getElementById(indicatorId);
                
                // Check localStorage for saved state
                const savedState = localStorage.getItem(contentId + '-state');
                
                if (savedState === 'open') {
                    content.style.display = 'block';
                    if (indicator) indicator.classList.add('rotate-down');
                } else {
                    content.style.display = 'none';
                    if (indicator) indicator.classList.remove('rotate-down');
                }
            });
        });
        
        // Modal functionality
        function openEditModal(program, tech, year, courseId, courseName) {
            document.getElementById('edit_program').value = program;
            document.getElementById('edit_tech').value = tech;
            document.getElementById('edit_year').value = year;
            document.getElementById('edit_course_code').value = courseId;
            document.getElementById('original_course_code').value = courseId; // Store original code for comparison
            document.getElementById('edit_course_name').value = courseName;
            
            // Set semester based on course code
            const lastDigit = courseId.slice(-1);
            if (lastDigit === '1') {
                document.getElementById('edit_semester').value = 'Semester 1';
            } else if (lastDigit === '2') {
                document.getElementById('edit_semester').value = 'Semester 2';
            }
            
            // Show the modal
            document.getElementById('editModal').style.display = 'block';
        }
        
        function closeEditModal() {
            document.getElementById('editModal').style.display = 'none';
        }
        
        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('editModal');
            if (event.target === modal) {
                closeEditModal();
            }
        }
        
        // Cascading dropdowns
        function loadTechs(program, targetId) {
            const techSelect = document.getElementById(targetId);
            techSelect.innerHTML = '<option value="">Select Technology</option>';
            
            if (program) {
                // Get technologies for selected program from database via AJAX
                fetch('get_technologies.php?program=' + encodeURIComponent(program))
                    .then(response => response.json())
                    .then(technologies => {
                        if (technologies.length > 0) {
                            technologies.forEach(tech => {
                                const option = document.createElement('option');
                                option.value = tech;
                                option.textContent = tech;
                                techSelect.appendChild(option);
                            });
                        } else {
                            techSelect.innerHTML = '<option value="">No technologies found</option>';
                        }
                    })
                    .catch(error => {
                        console.error('Error fetching technologies:', error);
                        techSelect.innerHTML = '<option value="">Error loading technologies</option>';
                    });
            }
            
            // If this was called for course tech dropdown, also update years
            if (targetId === 'tech_course') {
                loadYears();
            }
        }
        
        function loadYears() {
            const program = document.getElementById('program_course').value;
            const tech = document.getElementById('tech_course').value;
            const yearSelect = document.getElementById('year_course');
            
            yearSelect.innerHTML = '<option value="">Select Year</option>';
            
            if (!program || !tech) {
                console.log("Missing program or tech for loadYears", { program, tech });
                return;
            }
            
            console.log(`Loading years for ${program} and ${tech}`);
            yearSelect.innerHTML = '<option value="">Loading...</option>';
            
            // Get years for selected program and technology from database via AJAX
            fetch('get_years.php?program=' + encodeURIComponent(program) + '&technology=' + encodeURIComponent(tech))
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`Server error: ${response.status}`);
                    }
                    return response.json();
                })
                .then(years => {
                    console.log("Years returned:", years);
                    yearSelect.innerHTML = '<option value="">Select Year</option>';
                    
                    if (Array.isArray(years) && years.length > 0) {
                        years.forEach(year => {
                            const option = document.createElement('option');
                            option.value = year;
                            option.textContent = year;
                            yearSelect.appendChild(option);
                        });
                    } else {
                        console.warn("No years found for", program, tech);
                        yearSelect.innerHTML = '<option value="">No years found</option>';
                        
                        // Add a warning message to the form
                        const formGroup = yearSelect.closest('.form-group');
                        const warningElement = document.createElement('div');
                        warningElement.className = 'error-message';
                        warningElement.innerHTML = 'No years found for this program and technology. Please add a year level first.';
                        
                        // Remove existing warnings
                        const existingWarning = formGroup.querySelector('.error-message');
                        if (existingWarning) {
                            formGroup.removeChild(existingWarning);
                        }
                        
                        formGroup.appendChild(warningElement);
                    }
                })
                .catch(error => {
                    console.error('Error fetching years:', error);
                    yearSelect.innerHTML = '<option value="">Error loading years</option>';
                    
                    // Add error message
                    const formGroup = yearSelect.closest('.form-group');
                    const errorElement = document.createElement('div');
                    errorElement.className = 'error-message';
                    errorElement.innerHTML = 'Error: ' + error.message;
                    
                    // Remove existing errors
                    const existingError = formGroup.querySelector('.error-message');
                    if (existingError) {
                        formGroup.removeChild(existingError);
                    }
                    
                    formGroup.appendChild(errorElement);
                });
        }
        
        // At the beginning of the script section
        console.log('Course data:', <?php echo json_encode($courses); ?>);
        
        // Force refresh the view tab when it's clicked
        document.querySelector('.tab[onclick="showTab(\'view\')"]').addEventListener('click', function() {
            // Reload the page with a special parameter to show the view tab
            window.location.href = 'manage_courses.php?tab=view&t=' + new Date().getTime();
        });
        
        // On page load, check if we should show the view tab
        document.addEventListener('DOMContentLoaded', function() {
            const urlParams = new URLSearchParams(window.location.search);
            if (urlParams.get('tab') === 'view') {
                showTab('view');
            }
            
            // Initialize dropdowns on page load if values are already selected
            const programCourse = document.getElementById('program_course');
            const techCourse = document.getElementById('tech_course');
            
            if (programCourse && programCourse.value) {
                loadTechs(programCourse.value, 'tech_course');
                
                if (techCourse && techCourse.value) {
                    loadYears();
                }
            }
            
            // Add an additional event listener to ensure years are loaded after tech selection
            if (techCourse) {
                techCourse.addEventListener('change', loadYears);
            }
        });
        
        // Add function to automatically update course code based on semester selection
        document.getElementById('semester').addEventListener('change', function() {
            const courseCodeInput = document.getElementById('course_code');
            const courseCode = courseCodeInput.value;
            
            if (courseCode.length > 0) {
                const lastDigit = courseCode.slice(-1);
                if (this.value === 'Semester 1' && lastDigit === '2') {
                    courseCodeInput.value = courseCode.slice(0, -1) + '1';
                } else if (this.value === 'Semester 2' && lastDigit === '1') {
                    courseCodeInput.value = courseCode.slice(0, -1) + '2';
                }
            }
        });
        
        // Add a listener for the edit form submission
        document.getElementById('editCourseForm').addEventListener('submit', function(e) {
            const courseCode = document.getElementById('edit_course_code').value;
            const semester = document.getElementById('edit_semester').value;
            
            // Update course code based on semester selection if needed
            if (semester === "Semester 1" && courseCode.slice(-1) === "2") {
                document.getElementById('edit_course_code').value = courseCode.slice(0, -1) + "1";
            } else if (semester === "Semester 2" && courseCode.slice(-1) === "1") {
                document.getElementById('edit_course_code').value = courseCode.slice(0, -1) + "2";
            }
        });
    </script>
</body>
</html> 