<?php
// Start session before including any files that might try to modify session settings
session_start();

// Define secure access constant for config.php
define('SECURE_ACCESS', true);

// Include configuration
require_once '../config/config.php';

// Session timeout check using the function from security_helpers.php
if (session_expired()) {
    session_unset();
    session_destroy();
    header("Location: ../public/login.php");
    exit();
}

// Check if user is admin
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    header("Location: ../public/login.php");
    exit();
}

require_once '../config/db_conn.php';

$search = isset($_GET['search']) ? $_GET['search'] : '';
$sql = "SELECT u.*, s.first_name, s.last_name
        FROM users u
        LEFT JOIN students s ON u.student_id = s.student_id
        WHERE u.username LIKE ?
        OR CONCAT(s.first_name, ' ', s.last_name) LIKE ?
        ORDER BY u.user_id DESC"; // This will show newest users first
$stmt = $conn->prepare($sql);
$searchTerm = "%$search%";
$stmt->bind_param("ss", $searchTerm, $searchTerm);
$stmt->execute();
$result = $stmt->get_result();
?>

<!DOCTYPE html>
<html lang="en">
<head>

    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="DBTI Online Admin Dashboard">
    <link rel="icon" href="../public/assets/img/logo.webp" type="image/png">
    <title>Admin Dashboard | DBTI Online</title>
    <link rel="stylesheet" href="../public/assets/css/styles.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap');

* {
    padding: 0;
    margin: 0;
    box-sizing: border-box;
    font-family: Arial, sans-serif;
}

body {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}



        /* Styling for search bar and Add New User button */
        .search-bar {
            position: fixed;
            top: 80px;
            left: 0;
            right: 0;
            z-index: 999;
            background: white;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .search-bar form {
            display: flex;
            align-items: center;
        }
        .search-bar input[type="text"] {
            padding: 8px;
            font-size: 14px;
            margin-right: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .search-bar button, .search-bar .btn {
            padding: 8px 15px;
            font-size: 14px;
            color: #fff;
            background-color: #007bff;
            border: none;
            border-radius: 4px;
            text-decoration: none;
            cursor: pointer;
        }
        .search-bar .btn:hover {
            background-color: #0056b3;
        }
/* Top Container with Search and Add User */
.top-container {
    width: 98%;
    margin: 90px auto 20px;
    padding: 20px;
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* Table Container */
.table-container {
    width: 98%;
    margin: 0 auto 100px;
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 8px 16px rgba(0,0,0,0.1);
    overflow: auto;
    height: calc(100vh - 300px);
}
/* Table styling to make it wider */
.user-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    font-family: 'Inter', sans-serif;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin: 20px 0;
    border-radius: 8px;
    overflow: hidden;
}

.user-table thead tr {
    background-color: #4CAF50;
    color: white;
    font-weight: 600;
}

.user-table th {
    padding: 15px;
    text-align: left;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.user-table td {
    padding: 15px;
    border-bottom: 1px solid #edf2f7;
    font-size: 14px;
}

/* Mobile Responsive Updates */
@media screen and (max-width: 768px) {
    .container {
        padding: 10px;
        margin-bottom: 120px;
    }

    .user-table {
        display: block;
        overflow-x: auto;
        white-space: nowrap;
    }

    .user-table thead tr {
        display: table-row;
    }

    .user-table th,
    .user-table td {
        min-width: 120px;
        padding: 12px;
    }

    .search-bar {
        flex-direction: column;
        gap: 10px;
    }

    .search-bar form {
        width: 100%;
    }

    .search-bar input[type="text"] {
        width: 100%;
        margin-bottom: 10px;
    }

    .search-bar .btn {
        width: 100%;
        text-align: center;
    }
}

        /* Modal styling */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
        }
        .modal-content {
            background: white;
            margin: 15% auto;
            padding: 20px;
            width: 50%;
        }

                /* Footer */
footer {
    background-color: #333;
    color: white;
    padding: 12px 0;
    position: fixed;
    bottom: 0;
    width: 100%;
    z-index: 1000;
}

footer .footer {
    max-width: 98%;
    margin: 0 auto;
    text-align: center;
    font-size: 14px;
}

footer a {
    color: #ffbf00;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

footer a:hover {
    color: #ffd700;
}

@media screen and (max-width: 768px) {
    footer {
        padding: 8px 0;
    }

    footer .footer {
        font-size: 12px;
        padding: 0 15px;
    }
}


/* Updated Navbar Styles */
nav {
    background-color: #ffbf00;
    padding: 15px 4%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

nav .logo {
    height: 45px;
    width: auto;
}

nav .heading {
    font-size: 1.5rem;
    font-weight: 600;
    color: #fff;
}

nav ul {
    display: flex;
    gap: 15px;
    margin: 0;
    padding: 0;
}

nav ul li a {
    font-size: 0.9rem;
    padding: 8px 16px;
    background-color: #cc9900;
    border-radius: 4px;
    transition: all 0.2s ease;
    color: #fff;
    text-decoration: none;
    text-transform: uppercase;
    font-weight: 600;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.hamburger div {
    width: 25px;
    height: 3px;
    background-color: white;
    margin: 4px 0;
    transition: 0.4s;
}

/* Mobile Styles */
@media screen and (max-width: 768px) {
    nav {
        padding: 12px 4%;
    }

    nav .heading {
        font-size: 1.2rem;
    }

    nav .logo {
        height: 35px;
    }

    nav ul {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        width: 100%;
        background-color: #ffbf00;
        padding: 10px 0;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    nav ul.active {
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    nav ul li a {
        width: 180px;
        text-align: center;
        margin: 5px 0;
    }

    .hamburger {
        display: flex;
        flex-direction: column;
        cursor: pointer;
    }

    .hamburger div {
        width: 25px;
        height: 3px;
        background-color: white;
        margin: 4px 0;
        transition: 0.4s;
    }
}

/* Enhanced Hover Effects */
nav ul li a:hover {
    background-color: #996600;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.delete-btn {
    background-color: #dc3545;
    margin-left: 8px;
}

.delete-btn:hover {
    background-color: #c82333;
}

    </style>
</head>
<body>
<!-- Navigation Bar -->
<nav>
    <img src="../public/assets/img/logo.webp" alt="DBTI Logo" class="logo">
    <div class="heading">DBTI Online Registration</div>
    <div class="hamburger" onclick="toggleNavbar()">
        <div></div>
        <div></div>
        <div></div>
    </div>
    <ul id="navbar">
        <li><a href="../public/index.php">Home</a></li>
        <li><a href="../public/about.php">About</a></li>
        <li><a href="admin_dashboard.php">Dashboard</a></li>
        <li><a href="../public/logout.php">Logout <i class="fa-solid fa-user"></i></a></li>
    </ul>
</nav>



<script>
    function toggleNavbar() {
        const navbar = document.getElementById('navbar');
        navbar.classList.toggle('active');
    }
</script>

<div class="top-container">
    <form method="GET" class="search-form">
        <input type="text" name="search" placeholder="Search by username or name" value="<?= htmlspecialchars($search) ?>">
        <button type="submit">Search</button>
    </form>
    <a href="../src/Controllers/add_users.php" class="btn">Add New User</a>
</div>

<div class="table-container">
    <table class="user-table">
        <thead>
            <tr>
                <th>Full Name</th>
                <th>Username</th>
                <th>Role</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            <?php while ($row = $result->fetch_assoc()): ?>
                <tr>
                    <td><?= htmlspecialchars($row['first_name'] . ' ' . $row['last_name']) ?></td>
                    <td><?= htmlspecialchars($row['username']) ?></td>
                    <td><?= htmlspecialchars($row['role']) ?></td>
                    <td data-label="Actions">
                        <button onclick="openEditModal(<?= $row['user_id'] ?>)" class="btn edit-btn">Edit</button>
                        <button onclick="confirmDelete(<?= $row['user_id'] ?>)" class="btn delete-btn">Delete</button>
                    </td>
                </tr>
            <?php endwhile; ?>
        </tbody>
    </table>
</div>

<!-- Modal for editing user password -->
<div id="editModal" class="modal">
    <div class="modal-content">
        <h2>Edit User Password</h2>
        <form id="editForm" action="edit_user.php" method="POST">
            <input type="hidden" id="editUserId" name="user_id">
            <label for="new_password">New Password:</label>
            <input type="password" name="new_password" required>
            <button type="submit">Update Password</button>
            <button type="button" onclick="closeEditModal()">Cancel</button>
        </form>
    </div>
</div>

<footer>
    <div class="footer">
        <span>
            Copyright © 2024 Don Bosco Technological Institute. All Rights Reserved.
            <a href="https://www.dbti.ac.pg/" target="_blank">
                DBTI Website
            </a>
        </span>
    </div>
</footer>

<script>
    function openEditModal(userId) {
        document.getElementById('editUserId').value = userId;
        document.getElementById('editModal').style.display = 'block';
    }

    function closeEditModal() {
        document.getElementById('editModal').style.display = 'none';
    }


    function confirmDelete(userId) {
        if (confirm('Are you sure you want to delete this user?')) {
            fetch('delete_user.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'user_id=' + userId
            })
            .then(response => response.text())
            .then(result => {
                if (result === 'success') {
                    location.reload();
                } else if (result === 'protected') {
                    alert('Admin accounts cannot be deleted');
                } else {
                    alert('Error deleting user');
                }
            });
        }
    }


</script>


</body>
</html>
